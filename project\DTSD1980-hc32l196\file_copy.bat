
::ff
@echo off
@setlocal ENABLEDELAYEDEXPANSION

::获取当前项目文件夹以及上一级目录
pushd %~dp0 & for %%i in (.) do set target=%%~nxi
cd ..\
pushd %~dp1 & for %%i in (.) do set proj=%%~nxi
cd %~dp0

::获取传参
@set MODE=%~1%
@set ver=%~2%

::获取日期 
@set year=%date:~0,4%
@set month=%date:~5,2%
@set day=%date:~8,2%

if "%time:~0,1%"==" " (set hour=0"%time:~1,1%") else (set hour=%time:~0,2%)
@set minute=%time:~3,2%
@set second=%time:~6,2%
 
::查找固件版本号
for /f tokens^=3 %%a in ('findstr "#define.METER_SOFT_VER"     ..\..\source\ver.h')      do set soft_ver_tmp=%%a
@set soft_ver=%soft_ver_tmp:~1,5%

::建立项目输出文件夹
@set DIR_OUT=..\..\output\%target%
@set hex_path=%DIR_OUT%

if not exist %hex_path%	        mkdir   %hex_path%

@set out_file=.\Release\Exe
@set name=%MODE%-(%soft_ver%-%year%%month%%day%)

del %hex_path%\*.* /Q
::生成APP bin 文件，直接输出到output
ielftool.exe --verbose --bin=__ICFEDIT_APP_start__-__ICFEDIT_APP_end__ %out_file%\app.out %hex_path%\%MODE%-app(%soft_ver%-%year%%month%%day%).bin


::延时
echo off
set delay=3000
set TotalTime=0
set NowTime=%time%
::读取起始时间，时间格式为：13:01:05.95
::echo 程序开始时间：%NowTime%
:delay_continue
set /a minute1=1%NowTime:~3,2%-100
::读取起始时间的分钟数
set /a second1=1%NowTime:~-5,2%%NowTime:~-2%0-100000
::将起始时间的秒数转为毫秒
set NowTime=%time%
set /a minute2=1%NowTime:~3,2%-100
:: 读取现在时间的分钟数
set /a second2=1%NowTime:~-5,2%%NowTime:~-2%0-100000
::将现在时间的秒数转为毫秒
set /a TotalTime+=(%minute2%-%minute1%+60)%%60*60000+%second2%-%second1%
if %TotalTime% lss %delay% goto delay_continue
::echo 程序结束时间：%time%
::echo 设定延迟时间：%delay%毫秒
::echo 实际延迟时间：%TotalTime%毫秒

::copy hex文件到output
copy %out_file%\app.hex  %hex_path%\%name%.hex

::echo %soft_ver% >%hex_path%\name.txt
::echo %soft_ver_tmp% >%hex_path%\name2.txt