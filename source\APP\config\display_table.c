/**
 ******************************************************************************
 * @file    display_table.h
 * <AUTHOR> @date    2024
 * @brief   电表显示总表，包含所有可显示的电表数据
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/
#include "DLT645_2007_id.h"
#include "display_table.h"


/// 普通数据显示对象
#define DISP_NORMAL_ID(id, format) {id,format},


// typedef enum
// {
//     UNIT_YEAR         = 1,
//     UNIT_MONTH        = 2,
//     UNIT_WEEK         = 3,
//     UNIT_DAY          = 4,
//     UNIT_HOUR         = 5,
//     UNIT_MINUTE       = 6,
//     UNIT_SECOND       = 7,
//     UNIT_ANGLE        = 8,    // 度
//     UNIT_TEMP         = 9,    // 温度
//     UNIT_CURRENCY     = 10,   // 货币(local)
//     UNIT_m3           = 14,   // 用水量(m³)
//     UNIT_W            = 27,   // 有功功率,W
//     UNIT_kW           = 28,   // 有功功率,kW
//     UNIT_VA           = 29,   // 视在功率,W
//     UNIT_kVA          = 30,   // 视在功率,kW
//     UNIT_VAR          = 31,   // 无功功率,W
//     UNIT_kVAR         = 32,   // 无功功率,kW
//     UNIT_kWh          = 33,   // 有功电能,WH
//     UNIT_kVAh         = 34,   // 视在电能,vAh
//     UNIT_kvarh        = 35,   // 无功电能,varh
//     UNIT_A            = 36,   // A
//     UNIT_V            = 38,   // V
//     UNIT_Hz           = 47,   // Hz
//     UNIT_100          = 51,   // %
//     UNIT_RESERVED     = 253,  // reserved
//     UNIT_CONFIG       = 254,  // other unit
//     UNIT_UNDEF        = 255   // no unit, unitless, count
// } class_unit_t;

// typedef enum
// {
    // DF_UNDEFINED = 0,   // 无效格式
    // DF_OTHER,           // 其他格式
    // DF_ENERGY,          // 电能显示
    // DF_POWER,           // 功率显示
    // DF_CURRENT,         // 电流显示
    // DF_VOLTAGE,         // 电压显示
    // DF_MULTIPY_CT,      // 乘以电流变比
    // DF_MULTIPY_VT,      // 乘以电压变比
    // DF_MULTIPY_PT,      // 乘以电流*电压变比
    // DF_HEX_STRING,      // HEX串
    // DF_CHR_STRING,      // 字符串 ascii
    // DF_DATE,            // 日期格式: YYYY-MM-DD
    // DF_TIME,            // 时间格式: hh:mm:ss
    // DF_DATE_TIME,       // 日期时间分屏格式
// } dis_dat_typ_t;

/// 显示对象格式定义
#define DISP_FORMAT(dtyp,type,unit,num)    ((uint32_t)dtyp+((uint32_t)type<<8)+((uint32_t)unit<<16)+((uint32_t)num<<24)),

const disp_obj_s disp_total_table[] = 
{
    DISP_NORMAL_ID(ID_LCD_ALL,                  DISP_FORMAT(DT_NULL_DATA,               DF_CHR_STRING,  UNIT_UNDEF, 0))     /// LCD test, 全显示
    DISP_NORMAL_ID(C0_CMB_kWh(0,0),             DISP_FORMAT(DT_DOUBLE_LONG,             DF_ENERGY,      UNIT_kWh,   0))     /// 当前累计消耗组合有功电能总
    DISP_NORMAL_ID(C0_POS_kWh(0,0),             DISP_FORMAT(DT_DOUBLE_LONG,             DF_ENERGY,      UNIT_kWh,   0))     /// 当前累计消耗正向有功电能总
    DISP_NORMAL_ID(C0_NEG_kWh(0,0),             DISP_FORMAT(DT_DOUBLE_LONG,             DF_ENERGY,      UNIT_kWh,   0))     /// 当前累计消耗反向有功电能总

    DISP_NORMAL_ID(C2_A_VOL,                    DISP_FORMAT(DT_FLOAT32,                 DF_VOLTAGE,     UNIT_V,     0))     /// A相电压
#if defined(POLYPHASE_METER)
    DISP_NORMAL_ID(C2_B_VOL,                    DISP_FORMAT(DT_FLOAT32,                 DF_VOLTAGE,     UNIT_V,     0))     /// B相电压
    DISP_NORMAL_ID(C2_C_VOL,                    DISP_FORMAT(DT_FLOAT32,                 DF_VOLTAGE,     UNIT_V,     0))     /// C相电压
#endif
    DISP_NORMAL_ID(C2_A_CUR,                    DISP_FORMAT(DT_FLOAT32,                 DF_CURRENT,     UNIT_A,     0))     /// A相   电流
#if defined(POLYPHASE_METER)
    DISP_NORMAL_ID(C2_B_CUR,                    DISP_FORMAT(DT_FLOAT32,                 DF_CURRENT,     UNIT_A,     0))     /// B相   电流
    DISP_NORMAL_ID(C2_C_CUR,                    DISP_FORMAT(DT_FLOAT32,                 DF_CURRENT,     UNIT_A,     0))     /// C相   电流
#endif
    DISP_NORMAL_ID(C2_T_INS_P,                  DISP_FORMAT(DT_FLOAT32,                 DF_POWER,       UNIT_kW,    0))     /// 总    有功功率
#if defined(POLYPHASE_METER)
    DISP_NORMAL_ID(C2_A_INS_P,                  DISP_FORMAT(DT_FLOAT32,                 DF_POWER,       UNIT_kW,    0))     /// A相   有功功率
    DISP_NORMAL_ID(C2_B_INS_P,                  DISP_FORMAT(DT_FLOAT32,                 DF_POWER,       UNIT_kW,    0))     /// B相   有功功率
    DISP_NORMAL_ID(C2_C_INS_P,                  DISP_FORMAT(DT_FLOAT32,                 DF_POWER,       UNIT_kW,    0))     /// C相   有功功率
#endif
    DISP_NORMAL_ID(C2_T_INS_Q,                  DISP_FORMAT(DT_FLOAT32,                 DF_POWER,       UNIT_kvar,  0))     /// 总    无功功率
#if defined(POLYPHASE_METER)
    DISP_NORMAL_ID(C2_A_INS_Q,                  DISP_FORMAT(DT_FLOAT32,                 DF_POWER,       UNIT_kvar,  0))     /// A相   无功功率
    DISP_NORMAL_ID(C2_B_INS_Q,                  DISP_FORMAT(DT_FLOAT32,                 DF_POWER,       UNIT_kvar,  0))     /// B相   无功功率
    DISP_NORMAL_ID(C2_C_INS_Q,                  DISP_FORMAT(DT_FLOAT32,                 DF_POWER,       UNIT_kvar,  0))     /// C相   无功功率
#endif
    DISP_NORMAL_ID(C2_T_INS_S,                  DISP_FORMAT(DT_FLOAT32,                 DF_POWER,       UNIT_kVA,   0))     /// 总    视在功率
#if defined(POLYPHASE_METER)
    DISP_NORMAL_ID(C2_A_INS_S,                  DISP_FORMAT(DT_FLOAT32,                 DF_POWER,       UNIT_kVA,   0))     /// A相   视在功率
    DISP_NORMAL_ID(C2_B_INS_S,                  DISP_FORMAT(DT_FLOAT32,                 DF_POWER,       UNIT_kVA,   0))     /// B相   视在功率
    DISP_NORMAL_ID(C2_C_INS_S,                  DISP_FORMAT(DT_FLOAT32,                 DF_POWER,       UNIT_kVA,   0))     /// C相   视在功率
#endif
    DISP_NORMAL_ID(C2_T_PF,                     DISP_FORMAT(DT_FLOAT32,                 DF_DECIMAL_3,   UNIT_UNDEF, 0))     /// 总    功率因素
#if defined(POLYPHASE_METER)
    DISP_NORMAL_ID(C2_A_PF,                     DISP_FORMAT(DT_FLOAT32,                 DF_DECIMAL_3,   UNIT_UNDEF, 0))     /// A相   功率因素
    DISP_NORMAL_ID(C2_B_PF,                     DISP_FORMAT(DT_FLOAT32,                 DF_DECIMAL_3,   UNIT_UNDEF, 0))     /// B相   功率因素
    DISP_NORMAL_ID(C2_C_PF,                     DISP_FORMAT(DT_FLOAT32,                 DF_DECIMAL_3,   UNIT_UNDEF, 0))     /// C相   功率因素
#endif
    DISP_NORMAL_ID(C2_A_ANGEL,                  DISP_FORMAT(DT_FLOAT32,                 DF_DECIMAL_1,   UNIT_ANGLE, 0))     /// A相   相角
#if defined(POLYPHASE_METER)
    DISP_NORMAL_ID(C2_B_ANGEL,                  DISP_FORMAT(DT_FLOAT32,                 DF_DECIMAL_1,   UNIT_ANGLE, 0))     /// B相   相角
    DISP_NORMAL_ID(C2_C_ANGEL,                  DISP_FORMAT(DT_FLOAT32,                 DF_DECIMAL_1,   UNIT_ANGLE, 0))     /// C相   相角
#endif
    DISP_NORMAL_ID(C2_N_CUR,                    DISP_FORMAT(DT_FLOAT32,                 DF_CURRENT,     UNIT_A,     0))     /// 零线电流
    DISP_NORMAL_ID(C2_FREQUENVE,                DISP_FORMAT(DT_FLOAT32,                 DF_DECIMAL_2,   UNIT_Hz,    0))     /// 电网频率
    DISP_NORMAL_ID(C2_AVR_POWER_1M,             DISP_FORMAT(DT_FLOAT32,                 DF_POWER,       UNIT_kW,    0))     /// 一分钟有功平均功率
    DISP_NORMAL_ID(C2_CUR_DM_kW,                DISP_FORMAT(DT_FLOAT32,                 DF_POWER,       UNIT_kW,    0))     /// 当前有功需量
    DISP_NORMAL_ID(C2_CUR_DM_kva,               DISP_FORMAT(DT_FLOAT32,                 DF_POWER,       UNIT_kvar,  0))     /// 当前无功需量
    DISP_NORMAL_ID(C2_CUR_DM_kV,                DISP_FORMAT(DT_FLOAT32,                 DF_POWER,       UNIT_kVA,   0))     /// 当前视在需量
    DISP_NORMAL_ID(C2_TEMPARETURE,              DISP_FORMAT(DT_LONG,                    DF_DECIMAL_1,   UNIT_TEMP,  0))     /// 温度
    DISP_NORMAL_ID(C2_INTBAT_VOL,               DISP_FORMAT(DT_LONG,                    DF_DECIMAL_1,   UNIT_V,     0))     /// 内部时钟电池电压
    DISP_NORMAL_ID(C2_EXTBAT_VOL,               DISP_FORMAT(DT_LONG,                    DF_DECIMAL_1,   UNIT_V,     0))     /// 外部抄表电池电压
    DISP_NORMAL_ID(C2_INTBAT_RUN_TIME,          DISP_FORMAT(DT_DOUBLE_LONG_UNSIGNED,    DF_DECIMAL_0,   UNIT_UNDEF, 0))     /// 内部电池工作时间
    DISP_NORMAL_ID(C2_CUR_STEP_PRICE,           DISP_FORMAT(DT_INTEGER_UNSIGNED,        DF_DECIMAL_0,   UNIT_UNDEF, 0))     /// 当前阶梯电价

    DISP_NORMAL_ID(C4_DATE_WEEK,                DISP_FORMAT(DT_DATE,                    DF_DATE,        UNIT_UNDEF, 0))     /// 当前日期
    DISP_NORMAL_ID(C4_TIME,                     DISP_FORMAT(DT_TIME,                    DF_TIME,        UNIT_UNDEF, 0))     /// 当前日期
    DISP_NORMAL_ID(C4_COMM_ADDR,                DISP_FORMAT(DT_OCTET_STRING,            DF_HEX_STRING,  UNIT_UNDEF, 0))     /// 当前日期
    DISP_NORMAL_ID(C4_METER_NO,                 DISP_FORMAT(DT_OCTET_STRING,            DF_HEX_STRING,  UNIT_UNDEF, 0))     /// 当前日期


};

const uint16_t disp_total_table_num = eleof(disp_total_table);  //显示总表数量

// end of file
