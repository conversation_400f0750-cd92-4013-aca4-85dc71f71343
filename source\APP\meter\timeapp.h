#ifndef _TIME_APP_H
#define _TIME_APP_H

#include "typedef.h"


#define RTC_SUNDAY_IS_0  TRUE
#define BASE_YEAR             2000

typedef uint8_t CLOCK_TYPE;
#define CLOCK_YMDWhms  0xFE
#define CLOCK_YMDhms   0xEE
#define CLOCK_YMDhm    0xEC
#define CLOCK_MDhm     0x6C

/* 公历日期定义 */
typedef struct
{
    uint8_t year;
    uint8_t month;
    uint8_t day;
    uint8_t week;
}CalendarDate_s;

/* 公历时间定义 */
typedef struct
{
    uint8_t hour;
    uint8_t minute;
    uint8_t second;
}CalendarTime_s;

/* 公历定义 */
typedef struct
{
    CalendarDate_s date;
    CalendarTime_s time;
}Calendar_s;

typedef enum
{
    DT_EQUAL   = 0,
    DT_LARGER  = 1,
    DT_SMALLER = 2,
} DT_COMPARE_TYPE;

/// @brief 时钟状态字定义
typedef union
{
    struct
    {
        uint8_t invalid_value         : 1;  // 时钟失效 乱码
        uint8_t doubtful_value        : 1;  // 时钟可疑 上电时间 上电时刻时间小于掉电时间或大于掉电时间1000天
        uint8_t shift_over_limit      : 1;  // 广播校时偏差超过最大值

        uint8_t clock_error           : 1;  // 时钟故障:当电能表时钟在运行过程或停上电过程中发生倒退、格式错乱、
                                            // 上电时刻时间小于掉电时间或大于掉电时间1000天等情况时记录的事件。
                                            // 当电能表收到明文方式广播校时指令时，如果广播校时的校时范围大于最大校时偏差（默认5分钟），电能表不
                                            // 接受校时，同时记录时钟故障事件；每个自然日因为该原因最多只生成一条时钟故障事件记录。

        uint8_t invalid_clock_status  : 1;  // 时钟状态字无效
    };
    uint8_t value;
} ClockStatus_s;

/// @brief 系统时钟定义
typedef struct
{
    union
    {
        Calendar_s cale;    // 日期时间
        struct
        {
            uint8_t year;      // 年, 0~99
            uint8_t month;     // 月, 1~12
            uint8_t day;       // 日, 1~31
            uint8_t week;      // 周, 1~7
            uint8_t hour;      // 时, 0~23
            uint8_t minute;    // 分, 0~59
            uint8_t second;    // 秒, 0~59
        };
    };
    ClockStatus_s stus;           // 状态字, FF表示忽略, 01表示时钟无效
    uint32_t      u32datetime;    // RTC 转换为秒数，基于2000.1.1
} clock_s;

typedef struct
{
    uint16_t chk;
    uint16_t crc;
    int16_t  rtc_ppm;               // RTC 误差 ppm
    uint16_t bc_limit_max;          // 接受广播校时的最大偏差，超过不接受校时，并置时钟错误（一天最多一次事件）
    uint16_t bc_limit_min;          // 接受广播校时的最小偏差，小于不接受校时
    uint16_t shift_limit_max;       // 接受校时的最大偏差，超过不接受校时
    uint16_t shift_limit_min;       // 接受校时的最小偏差，小于不接受校时
    uint8_t  bc_limit_num;          // 每天接受广播校时的次数，超过不接受校时
    uint8_t  shift_limit_num;       // 每天接受校时的次数，超过不接受校时
}ClockPara_s;

typedef struct
{
    uint16_t chk;
    uint16_t crc;
    clock_s  pwrdn_time;            // 掉电时间
    clock_s  shift_time;            // 上次校时时间前
    clock_s  bc_time;               // 上次广播校时前时间
    uint32_t up_time;               // 上电时间
    uint8_t  bc_err_num;            // 广播校时错误次数
    uint8_t  bc_num;                // 广播校时执行次数
}ClockData_s;

/// @brief 时钟状态字定义,用于事件记录
typedef uint16_t CLOCK_STUS;
#define STUS_CLOCK_PRG_SYNC_PARA     ((CLOCK_STUS)1 << 2 ) // 更改校时参数
#define STUS_CLOCK_SHIFT_EVENT       ((CLOCK_STUS)1 << 3 ) // 时钟调整事件
#define STUS_CLOCK_SHIFT_INVALID     ((CLOCK_STUS)1 << 4 ) // 时钟调整无效
#define STUS_CLOCK_BC_EVENT          ((CLOCK_STUS)1 << 5 ) // 广播校时事件
#define STUS_CLOCK_BC_INVALID        ((CLOCK_STUS)1 << 6 ) // 广播校时无效
#define STUS_CLOCK_INVALID           ((CLOCK_STUS)1 << 11) // 时钟失效
#define STUS_CLOCK_OVER_YEAR         ((CLOCK_STUS)1 << 12) // 时钟跨年
#define STUS_CLOCK_LOSS              ((CLOCK_STUS)1 << 13) // 时钟丢失



/// @brief 时钟模块接口
struct mclock_s
{
    /* 全局变量 */
    clock_s*  datetime;

    /* 流程控制接口 */
    void (*init)(void);
    /// @brief 秒任务
    void (*refresh)(void);
    /// @brief 掉电保存接口
    void (*pwr_down_save)(void);

    /* 数据初始化接口 */
    void (*reset)(uint8_t type);

    /* 事件状态输出接口 */
    bool (*state_query)(CLOCK_STUS state);
    void (*state_clr)(void);

    /// @brief 参数获取
    const ClockPara_s* (*para_get)(void);

    /// @brief 参数设置
    bool (*para_set)(uint16_t ofst, const void* val, uint16_t len);

    /// @brief 上次掉电时间获取
    void (*pdtime_get)(clock_s* clock);

    /// @brief 上次校时前时间获取
    void (*sync_time_get)(clock_s* clock);

    /// @brief 普通校时处理
    bool (*sync_time_set)(clock_s* clock);

    /// @brief 网络校时处理
    bool (*network_time_set)(clock_s* clock);

    /// @brief 广播校时处理
    bool (*bc_time_set)(clock_s* clock, uint8_t typ);

    /// @brief 时钟是否有效
    bool (*is_valid)(const clock_s* clock);

    /// @brief 时钟与系统当前时间比较, 0: 时钟时间等于系统时间，1: 时钟时间小于系统时间，-1: 时钟时间大于系统时间
    int8_t (*compare)(const clock_s* clock);

    /// @brief 设置时钟失效
    void (*invalid_set)(clock_s* clock);

    /// @brief 设置RTC误差
    void (*ppm_set)(int16_t ppm);

    /// @brief 获取RTC误差
    int16_t (*ppm_get)(void);    

    /// @brief 将全局秒数转换为系统时钟
    void (*seconds_to_calendar)(Calendar_s* calendar, uint32_t seconds);

    /// @brief 将系统时钟转换为全局秒数
    uint32_t (*calendar_to_seconds)(const Calendar_s* calendar);

    /// @brief 将系统时钟转换为BCD格式的时钟
    uint8_t (*format_to645)(uint8_t* buf, const clock_s* clock, CLOCK_TYPE typ);

    /// @brief 将BCD格式的时钟转换为系统时钟
    void (*unformat_frm645)(const uint8_t* buf, clock_s* clock, CLOCK_TYPE typ);

    /// @brief 将全局秒数转换为BCD格式的时钟
    uint8_t (*gseconds_to645)(uint8_t* buf, uint32_t gseconds, CLOCK_TYPE typ);

    /// @brief 比较两个时间的差值,大于0: des时间大于src时间，小于0: des时间小于src时间
    int32_t (*diff_value)(const clock_s* des, const clock_s* src);

    /// @brief 获取当月份天数
    uint8_t (*get_month_days)(uint16_t year, uint8_t month);

    /// @brief 获取星期值
    uint8_t (*get_week_value)(uint16_t year, uint8_t month, uint8_t day);

    /// @brief 时钟调整时区，
    bool (*zone_adjust)(clock_s* clock, int16_t zone);
};

extern const struct mclock_s mclock;



#endif // _TIME_APP_H



