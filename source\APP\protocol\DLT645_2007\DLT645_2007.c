/******************************************************************************
*    (c) Copyright 2016, SheWei Electronics Co.,Ltd.
*    All Rights Reserved
*
*    Filename:      DLT645_2007.c
*    Describe:      
*
*    Device:
*    Compiler:
*
*    Created on:
*    Modify record:
*
*******************************************************************************/
#include "utils.h"
#include "DLT645_2007.h"
#include "DLT645_2007_id.h"
#include "comm_phy.h"
#include "timeapp.h"
#include "utils.h"
#include "debug.h"
#include "protocol.h"
#include "..\..\interface\api.h"
#include "demand.h"
#include "datastore.h"
#include "sysinit.h"
#include "event.h"
#include "RelayApp.h"

#define DLT645_2007_REQUEST_LEN_MAX      200    /// 数据读取数据域最大长度
#define DLT645_2007_SET_LEN_MAX          50     /// 设置参数数域最大长度
#define DLT645_2007_CONNECT_TIMEOUT      3000   /// 分帧超时时间ms
#define DLT645_2007_FRAME_LEN            12     /// 协议占用帧长，68 + 6ADDR + 68 + 1CMD + 1LEN + 1CS + 16
#define DLT645_2007_FE_NUM               4      /// 发送FE个数
#define DLT645_2007_FRAME_LEN_MAX        (DLT645_2007_FRAME_LEN + DLT645_2007_FE_NUM)  /// 协议占用最大长度
  
static uint32_t last_opt_code;   /// 上次操作码

///广播
typedef enum
{
    CMD_NONE            = 0x00,     /// 00000：无效命令
    CMD_BROADCAST       = 0x08,     /// 01000：广播命令
    CMD_READ_DATA       = 0x11,     /// 10001：读取数据命令
    CMD_READ_NEXT_DATA  = 0x12,     /// 10010：读取后续数据
    CMD_READ_ADDR       = 0x13,     /// 10011：读通信地址
    CMD_WRITE_PARA      = 0x14,     /// 10100：写数据
    CMD_SET_ADDR        = 0x15,     /// 10101：写通信地址
    CMD_FROZEN          = 0x16,     /// 10110：冻结命令
    CMD_SET_BAUD        = 0x17,     /// 10111：更改通信速率
    CMD_SET_PASSWORD    = 0x18,     /// 11000：修改密码
    CMD_CLEAN_MD        = 0x19,     /// 11001：最大需量清零
    CMD_CLEAN_METER     = 0x1A,     /// 11010：电表清零
    CMD_CLEAN_EVENT     = 0x1B,     /// 11011：事件清零
    CMD_RELAY_CTRL      = 0x1C      /// 11100：继电器控制
}CMD_TYPE_t;

/// 错误信息字ERR：
#define ERR_CODE_REV        (1<<7)  /// 保留 
#define ERR_CODE_TARIFF_OVR (1<<6)  /// 费率数超 
#define ERR_CODE_SCH_OVR    (1<<5)  /// 日时段数超 
#define ERR_CODE_ZONE_OVR   (1<<4)  /// 年时区数超 
#define ERR_CODE_BAUD_W_ERR (1<<3)  /// 通信速率不能更改 
#define ERR_CODE_PSW_ERR    (1<<2)  /// 密码错/未授权 
#define ERR_CODE_NO_DATA    (1<<1)  /// 无请求数据 
#define ERR_CODE_OTHER      (1<<0)  /// 其他错误
#define ERR_CODE_NONE       0       /// 没有错误

typedef enum
{
    ADDR_TYPE_NULL      = 0x00,     /// 其他地址
    ADDR_TYPE_BROADCAST = 0x01,     /// 广播地址
    ADDR_TYPE_SUPER     = 0x02,     /// 超级地址
    ADDR_TYPE_SELF      = 0x03,     /// 匹配电表地址
}ADDR_TYPE_t;

typedef struct
{
    uint8_t    *recv;               /// 接收数据指针
    uint8_t    *data;               /// 数据域
    uint8_t    *send;               /// 发送数据指针
    uint8_t    *snd_dat;            /// 返回值数据域指针
    uint32_t    id;                 /// 帧ID
    uint32_t    id_last;            /// 上次读取ID
    uint16_t    timeout_tmr;        /// 帧超时计数器
    uint16_t    err_code;           /// 错误码
    uint8_t     data_len;           /// 数据长度
    uint8_t     data_len_max;       /// 发送数据域最大长度
    uint8_t     addr[6];            /// 地址
    CMD_TYPE_t  ctrl;               /// 控制位
    ADDR_TYPE_t addr_type;          /// 地址类型
    uint8_t     frame_no;           /// 帧序号
    bool        frame_f;            /// 分帧标志
    bool        err_f;              /// 应答标志，异常应当置1
    bool        is_97;              /// 是否97协议
} DLT645_2007_MSG_S;

DLT645_2007_MSG_S dlt07msg_info[PHY_CHN_NUM];

#include "dlt645_c0.c"
#include "dlt645_c1.c"
#include "dlt645_c2.c"
#include "dlt645_c3.c"
#include "dlt645_c4.c"
#include "dlt645_c5.c"
#include "dlt645_c6.c"
#include "dlt645_c7.c"
#include "dlt645_c1x.c"

static ADDR_TYPE_t dlt645_addr_check(uint8_t *addr)
{
    uint8_t buf[6];   

    // 广播地址
    memset(buf, 0x99, 6);
    if(memcmp(buf, addr, 6) == 0) return ADDR_TYPE_BROADCAST;

    // 超级地址
    memset(buf, 0xAA, 6);
    if(memcmp(buf, addr, 6) == 0) return ADDR_TYPE_SUPER;

    // 完全匹配地址
    // dlt645_addr_get(buf);
    if(memcmp(buf, addr, 6) == 0) return ADDR_TYPE_SELF;

    return ADDR_TYPE_NULL;
}
/// @brief 广播校时处理
/// @param p_info 
/// @return 
static bool dlt_645_broadcast_msg(DLT645_2007_MSG_S *p_info)
{
    uint8_t *p_data = p_info->data;
    clock_s clock;

    mclock.unformat_frm645(p_data, &clock, CLOCK_YMDhms);
    // DBG_PRINTF(P_645, D, "\r\n broadcast: 20%02d-%02d-%02d, %02d:%02d:%02d", clock.year, clock.month, clock.day, clock.hour, clock.minute, clock.second);
    return mclock.bc_time_set(&clock, 0);  /// 0-明文
}


/// @brief 读取数据处理
/// @param p_info 
/// @return 
static bool dlt_645_read_msg(DLT645_2007_MSG_S *p_info)
{
    // uint8_t *p_data = p_info->snd_dat;

    DBG_PRINTF(P_645, D, "\r\n DLT645_2007_read: ");
    switch (p_info->id & 0xFF000000)
    {
        case 0x00000000:
            // 00类数据
            p_info->data_len = dlt_645_read_0(p_info, NULL);
            break;
        case 0x01000000:
            // 01类数据
            p_info->data_len = dlt_645_read_1(p_info, NULL);
            break;
        case 0x02000000:
            // 02类数据
            p_info->data_len = dlt_645_read_2(p_info, NULL);
            break;
        case 0x03000000:
            // 03类数据
            p_info->data_len = dlt_645_read_3(p_info, NULL);
            break;
        case 0x04000000:
            // 04类数据
            p_info->data_len = dlt_645_read_4(p_info, NULL);
            break;
        case 0x05000000:
            // 05类数据
            p_info->data_len = dlt_645_read_5(p_info, NULL);
            break;
        case 0x06000000:
            // 06类数据
            p_info->data_len = dlt_645_read_6(p_info, NULL);
            break;
        case 0x07000000:
            // 07类数据
            break;
    }
    if((p_info->id & 0xF0000000) == 0x10000000) //事件
    {
        p_info->data_len = dlt_645_read_1x(p_info, NULL);
    }
    DBG_PRINTF(P_645, M, p_info->snd_dat, p_info->data_len);
    return TRUE;
}

/// @brief 读取后续数据
/// @param p_info 
/// @param data 
/// @param data_len 
/// @return 
static bool dlt_645_read_next_msg(DLT645_2007_MSG_S *p_info)
{
    if(p_info->id_last != p_info->id) return FALSE; /// 跟上次读取ID不一致

    return dlt_645_read_msg(p_info);
}

/// @brief 10011：读通信地址
/// @param p_info 
/// @return 
static bool dlt_645_read_addr_msg(DLT645_2007_MSG_S *p_info)
{
    uint8_t *p_send  = p_info->snd_dat;
    if((p_info->data_len != 0) || (p_info->addr_type < ADDR_TYPE_SUPER)) return FALSE; //超级地址或者匹配地址才有效，数据域为0
    api.meter_sn_get(p_send);
    p_info->data_len = 6;
    return TRUE;
}
        
/// @brief 10100：写数据
/// @param p_info 
/// @return 
static bool dlt_645_wrtie_msg(DLT645_2007_MSG_S *p_info)
{
    uint8_t *p_data = p_info->data;
    uint32_t opt_code;
    uint8_t ret = ERR_CODE_OTHER;
    uint8_t psw = 0;    //密码等级
    DBG_PRINTF(P_645, D, "\r\n DLT645_2007_read: ");

    p_data += 4; /// 跳过ID
    p_info->data_len -= 4; /// 减去ID
    psw = api.dlt645_password_query(p_data);

    if(psw == 0xFF) //密码错误
    {
        p_info->data_len = 1;
        *p_info->snd_dat = ERR_CODE_PSW_ERR;
        if(p_info->addr_type == ADDR_TYPE_BROADCAST) return FALSE;  /// 广播地址不应答
        else return TRUE;  
    }
    else if(psw == 0x98) //密文+MAC
    {

    }
    else if(psw == 0x99) //明文+MAC
    {

    }
    else // 密码正确
    {
        p_data += 4; /// 跳过密码
        if(p_info->data_len <= 8 || psw > 4) ///数据长度不足 或 密码等级不正确
        {
            p_info->data_len = 1;
            *p_info->snd_dat = ERR_CODE_PSW_ERR;
            if(p_info->addr_type == ADDR_TYPE_BROADCAST) return FALSE;  /// 广播地址不应答
            else return TRUE;  
        }
        memcpy(&opt_code, p_data, 4), p_data += 4; /// 跳过opt_code
        last_opt_code = opt_code;
        p_info->data_len -= 8; /// 减去密码 + opt_code
    }
    
    switch (p_info->id & 0xFF000000)
    {
        case 0x00000000:
            // 00类数据
            break;
        case 0x01000000:
            break;
        case 0x02000000:
            break;
        case 0x03000000:
            break;
        case 0x04000000:
            // 04类数据
            ret =  dlt_645_write_4(p_info, p_data, opt_code, psw);
            break;
        case 0x05000000:
            break;
        case 0x06000000:
            break;
        case 0x07000000:
            break;
        case 0x10000000:
            // 09类数据
            break;
    }

    if(ret) //错误应答
    {
        p_info->data_len = 1;
        *p_info->snd_dat = ret;
        p_info->err_f = TRUE;
    }
    else
    {
        p_info->data_len = 0;
    }

    if(p_info->addr_type == ADDR_TYPE_BROADCAST) return FALSE;  /// 广播地址不应答
    return TRUE;    
}       

      
/// @brief 10101：写通信地址
/// @param p_info 
/// @return 
static bool dlt_645_write_addr_msg(DLT645_2007_MSG_S *p_info)
{
    
    return TRUE;
}        

      
/// @brief 10110：冻结命令
/// @param p_info 
/// @return 
static bool dlt_645_frezon_msg(DLT645_2007_MSG_S *p_info)
{
    
    return TRUE;
}        

    
/// @brief 10111：更改通信速率
/// @param p_info 
/// @return 
static bool dlt_645_set_baud_msg(DLT645_2007_MSG_S *p_info)
{
    
    return TRUE;
}        

   
/// @brief 11000：修改密码
/// @param p_info 
/// @return 
static bool dlt_645_set_psw_msg(DLT645_2007_MSG_S *p_info)
{
    uint8_t *p_data = p_info->data;
    uint8_t psw;

    psw = api.dlt645_password_query(p_data);
    p_data += 4; /// 跳过密码
    if(psw > *p_data) //密码等级不正确
    {
        p_info->data_len = 1;
        *p_info->snd_dat = ERR_CODE_PSW_ERR;
        if(p_info->addr_type == ADDR_TYPE_BROADCAST) return FALSE;  /// 广播地址不应答
        else return TRUE; 
    }
    if(api.dlt645_password_set(p_data))
    {
        p_info->data_len = 0;
    }
    else
    {
        p_info->data_len = 1;
        *p_info->snd_dat = ERR_CODE_OTHER;
    }
    if(p_info->addr_type == ADDR_TYPE_BROADCAST) return FALSE;  /// 广播地址不应答
    return TRUE;
}        

     
/// @brief 11001：最大需量清零 需要至少4级密码
/// @param p_info 
/// @return 
static bool dlt_645_clean_md_msg(DLT645_2007_MSG_S *p_info)
{
    uint8_t *p_data = p_info->data;
    uint8_t psw;

    psw = api.dlt645_password_query(p_data);
    if(psw > 4)   //需要至少4级密码
    {
        p_info->data_len = 1;
        *p_info->snd_dat = ERR_CODE_PSW_ERR;
        if(p_info->addr_type == ADDR_TYPE_BROADCAST) return FALSE;  /// 广播地址不应答
        else return TRUE; 
    }
    p_data += 4; /// 跳过密码
    memcpy(&last_opt_code, p_data, 4); /// 保存opt_code

    sysinit.reset(SYS_MD_CLR);

    if(p_info->addr_type == ADDR_TYPE_BROADCAST) return FALSE;  /// 广播地址不应答

    p_info->data_len = 0;
    return TRUE;
}        

     
/// @brief 11010：电表清零
/// @param p_info 
/// @return 
static bool dlt_645_clean_meter_msg(DLT645_2007_MSG_S *p_info)
{
    uint8_t *p_data = p_info->data;
    uint8_t psw;
    
    psw = api.dlt645_password_query(p_data);
    if(psw > 2)   //需要至少2级密码
    {
        p_info->data_len = 1;
        *p_info->snd_dat = ERR_CODE_PSW_ERR;
        if(p_info->addr_type == ADDR_TYPE_BROADCAST) return FALSE;  /// 广播地址不应答
        else return TRUE; 
    }
    p_data += 4; /// 跳过密码
    memcpy(&last_opt_code, p_data, 4); /// 保存opt_code

    sysinit.reset(SYS_DATA_CLR);

    if(p_info->addr_type == ADDR_TYPE_BROADCAST) return FALSE;  /// 广播地址不应答

    p_info->data_len = 0;
    return TRUE;
}        

      
/// @brief 11011：事件清零
/// @param p_info 
/// @return 
static bool dlt_645_clean_event_msg(DLT645_2007_MSG_S *p_info)
{
    uint8_t  *p_data = p_info->data;
    uint32_t id;
    uint8_t  psw;
    
    psw = api.dlt645_password_query(p_data);
    if(psw > 2)   //需要至少2级密码
    {
        p_info->data_len = 1;
        *p_info->snd_dat = ERR_CODE_PSW_ERR;
        if(p_info->addr_type == ADDR_TYPE_BROADCAST) return FALSE;  /// 广播地址不应答
        else return TRUE; 
    }
    p_data += 4; /// 跳过密码
    memcpy(&last_opt_code, p_data, 4); /// 保存opt_code
    p_data += 4; /// 跳过opt_code
    memcpy(&id, p_data, 4); /// 保存ID

    event.group_clr_645(id, last_opt_code);

    if(p_info->addr_type == ADDR_TYPE_BROADCAST) return FALSE;  /// 广播地址不应答

    p_info->data_len = 0;
    return TRUE;
}

static bool dlt_645_relay_ctrl_msg(DLT645_2007_MSG_S *p_info)
{
    uint8_t *p_data = p_info->data;
    uint8_t psw;
#if USE_RLY
    RLY_TYPE_t relay_type;
#endif
    psw = api.dlt645_password_query(p_data);
    if(psw > 2)   //需要至少2级密码
    {
        p_info->data_len = 1;
        *p_info->snd_dat = ERR_CODE_PSW_ERR;
        if(p_info->addr_type == ADDR_TYPE_BROADCAST) return FALSE;  /// 广播地址不应答
        else return TRUE; 
    }
    p_data += 4; /// 跳过密码
#if RLY_INSIDE_ENABLE
    relay_type = TYPE_RLY_1;
#elif RLY_OUTSIDE_ENABLE
    relay_type = TYPE_RLY_2;
#elif RLY_ALARM_ENABLE
    relay_type = RLY_NUM;
#endif
#if USE_RLY
    if(control.remote((remote_ctrl_s)*(p_data + 4), relay_type, p_data))
    {
        p_info->data_len = 0;
    }
    else
    {
        p_info->data_len = 1;
        *p_info->snd_dat = ERR_CODE_OTHER;
    }
#else
    p_info->data_len = 0; /// 无继电器控制
#endif
    if(p_info->addr_type == ADDR_TYPE_BROADCAST) return FALSE;  /// 广播地址不应答
    return TRUE;
}
/// @brief 判断是否为DLT645_2007协议
/// @param len 数据帧长度
/// @return true:是DLT645_2007协议，false:不是DLT645_2007协议
static bool is_dlt645_2007_msg(DLT645_2007_MSG_S *p_info, uint16_t len)
{
    uint8_t data_len;
    uint8_t *ptr = p_info->recv;

    while(len >= DLT645_2007_FRAME_LEN)
    {
        if(*ptr == 0x68) break; ///查找帧头
        ptr++, len--;
    }
    if(len < DLT645_2007_FRAME_LEN) return false;    ///帧长度不足
    p_info->addr_type = dlt645_addr_check(ptr + 1);
    if(ptr[7] != 0x68 || (p_info->addr_type == ADDR_TYPE_NULL)) return false;  ///校验地址
    data_len = ptr[9], p_info->data_len = data_len;  ///数据域长度
    if(ptr[data_len + 10] != cal_checksum8(ptr, data_len + 10)) return false;  ///校验
    if(ptr[data_len + 11] != 0x16) return false;     ///结束符
    memcpy(p_info->addr, ptr + 1, 6);        ///地址
    p_info->data = ptr + 10;                 ///数据域指针
    p_info->ctrl = (CMD_TYPE_t)ptr[8];       ///控制字
    for(uint8_t i = 0; i < data_len; i++) ptr[10 + i] -= 0x33;  ///数据域-33
    
    p_info->frame_no = 0;
    p_info->frame_f  = false;
    p_info->err_f    = false;
    p_info->is_97    = false;
    p_info->timeout_tmr = DLT645_2007_CONNECT_TIMEOUT;
    memcpy((uint8_t *)&p_info->id, p_info->data, 4);

    if(p_info->ctrl == CMD_READ_DATA) { p_info->id_last = p_info->id;}
    else if(p_info->ctrl != CMD_READ_NEXT_DATA) p_info->id_last  = 0xFFFFFFFF;  /// 非读取后续数据，置为无效ID
    else if(p_info->ctrl == CMD_READ_NEXT_DATA) {p_info->frame_no = *(ptr + 14); if(p_info->frame_no == 0) return false;}; /// 帧序号
    return true;
}

static uint16_t dlt645_2007_response(DLT645_2007_MSG_S *p_info)
{
    uint8_t *p_send  = p_info->send;
    uint8_t data_len = p_info->data_len;
    uint8_t checksum;

    memset(p_send, 0xFE, DLT645_2007_FE_NUM), p_send += DLT645_2007_FE_NUM; /// 发送FE
    *p_send = 0x68, p_send++;
    // memcpy(p_send, p_info->addr, 6), p_send += 6; /// 复制地址
    api.meter_sn_get(p_send), p_send += 6;
    *p_send = 0x68, p_send++;
    if(p_info->frame_f) {p_info->ctrl |= 0x20; }  /// 有后续帧
    else {p_info->id_last = 0xFFFFFFFF; }   /// 无后续帧，置为无效ID
    if(p_info->err_f)   {p_info->ctrl |= 0x40; }  /// 异常应答
    *p_send = (p_info->ctrl | 0x80), p_send++;    /// 控制字
    *p_send = data_len, p_send++;                 /// 数据域长度
    memcpy(p_send, p_info->snd_dat, data_len);
    for(uint8_t i = 0; i < data_len; i++) p_send[i] += 0x33;
    p_send += data_len;
    checksum = cal_checksum8(p_info->send + DLT645_2007_FE_NUM, data_len + 10);
    *p_send = checksum, p_send++;
    *p_send = 0x16;
    return (data_len + 12 + DLT645_2007_FE_NUM);
}

/// @brief 初始化DLT645_2007协议
/// @param chn 通道号
/// @param buff 协议数据缓存区
/// @param  
void DLT645_2007_init(uint8_t chn, uint8_t *buff)
{
    dlt07msg_info[chn].recv    = buff;
    dlt07msg_info[chn].send    = buff;
    dlt07msg_info[chn].snd_dat = buff + DLT645_2007_FRAME_LEN_MAX;
    dlt07msg_info[chn].id      = 0;
    dlt07msg_info[chn].timeout_tmr = 0;
    dlt07msg_info[chn].frame_f     = false;
    dlt07msg_info[chn].data_len_max= DLT645_2007_REQUEST_LEN_MAX;
    dlt07msg_info[chn].id_last     = 0xFFFFFFFF;
}

/// @brief DLT645_2007 协议处理，需判断是否为2007协议，并进行协议解析。
/// @param msg 接收到的数据
/// @param len 接收到的数据长度
/// @return 需要发送的数据帧长度,0-不发送数据
uint16_t DLT645_2007_msg_process(uint8_t chn, uint8_t *ack, uint16_t len)
{
    DLT645_2007_MSG_S *p_info = &dlt07msg_info[chn];

    *ack = ACK_NULL; /// 未确认协议
    DBG_PRINTF(P_645, D, "\r\n DLT645_2007_msg_process: ");
    if(is_dlt645_2007_msg(p_info, len) == false) {return 0;}  ///不是DLT645协议
    DBG_PRINTF(P_645, D, "\r\n Frame info: addr_type:%d, ctrl:%02x, ID:%08x, data_len:%d, \r\n ->data: ", p_info->addr_type, p_info->ctrl, p_info->id, p_info->data_len);
    DBG_PRINTF(P_645, M, p_info->data, p_info->data_len);
    *ack = ACK_NO;  ///确认协议，但是默认不发送应答
    switch (p_info->ctrl)
    {
        case CMD_BROADCAST:         /// 01000：广播命令
            dlt_645_broadcast_msg(p_info);
            return 0;               /// 广播指令，不需要发送数据
        case CMD_READ_DATA:         /// 10001：读取数据命令
            if(dlt_645_read_msg(p_info) == false) return 0;
            break;
        case CMD_READ_NEXT_DATA:    /// 10010：读取后续数据
            if(dlt_645_read_next_msg(p_info) == false) return 0;
            break;
        case CMD_READ_ADDR:         /// 10011：读通信地址
            if(dlt_645_read_addr_msg(p_info) == false) return 0;
            break;
        case CMD_WRITE_PARA:        /// 10100：写数据
            if(dlt_645_wrtie_msg(p_info) == false) return 0;
            break;
        case CMD_SET_ADDR:          /// 10101：写通信地址
            if(dlt_645_write_addr_msg(p_info) == false) return 0;
            break;
        case CMD_FROZEN:            /// 10110：冻结命令
            if(dlt_645_frezon_msg(p_info) == false) return 0;
            break;
        case CMD_SET_BAUD:          /// 10111：更改通信速率
            if(dlt_645_set_baud_msg(p_info) == false) return 0;
            break;
        case CMD_SET_PASSWORD:      /// 11000：修改密码
            if(dlt_645_set_psw_msg(p_info) == false) return 0;
            break;
        case CMD_CLEAN_MD:          /// 11001：最大需量清零
            if(dlt_645_clean_md_msg(p_info) == false) return 0;
            break;
        case CMD_CLEAN_METER:       /// 11010：电表清零
            if(dlt_645_clean_meter_msg(p_info) == false) return 0;
            break;
        case CMD_CLEAN_EVENT:       /// 11011：事件清零
            if(dlt_645_clean_event_msg(p_info) == false) return 0;
            break;
        case CMD_RELAY_CTRL:
            if(dlt_645_relay_ctrl_msg(p_info) == false) return 0;
            break;
    }
    *ack = ACK_YES;
    return (dlt645_2007_response(p_info));
}

/// @brief 获取上次接收到的数据的opt_code
/// @param  
/// @return 
uint32_t DLT645_2007_get_opt_code(void)
{
    return last_opt_code;
}

const struct dlt645_2007_s DLT645 = {
    .init         = DLT645_2007_init,
    .get_opt_code = DLT645_2007_get_opt_code,
    .msg_process  = DLT645_2007_msg_process,
};

///
