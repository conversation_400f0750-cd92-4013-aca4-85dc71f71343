
#include "app.h"
#include "datastore.h"
#include "status.h"
#include "dispApp.h"
#include "RelayApp.h"
#include "ver.h"
#include "debug.h"
#include "energy.h"
#include "tariff.h"
#include "protocol.h"
#include "timeapp.h"
#include "demand.h"
#include "billing.h"
#include "loadcurve.h"
#if SW_PAYMENT_EN
#include "step_tariff.h"
#endif
#if HW_DCU_MODUL
#include "dcu.h"
#endif

#define TASK_PRINT_EN P_TASK

struct meter_func_t
{
    const struct app_task_t *task;
};

extern const struct app_task_t status_task;
extern const struct app_task_t fwm_upgrade_task;
extern const struct app_task_t demand_task;
extern const struct app_task_t billing_task;
extern const struct app_task_t loadcurve_task;
extern const struct app_task_t power_event_task;
extern const struct app_task_t control_task;
extern const struct app_task_t display_task;
extern const struct app_task_t protocol_task;
#if SW_PAYMENT_EN
extern const struct app_task_t step_tariff_task;
extern const struct app_task_t payment_task;
#endif
extern const struct app_task_t event_task;

/// @brief  模块任务列表
/// @note   任务列表中包含所有模块的任务，按顺序依次执行
/// @note   任务列表中任务的执行顺序，决定了模块的初始化和执行顺序，因此，任务列表中任务的执行顺序必须正确，
///         否则可能导致系统初始化失败或运行异常。 不要修改！！！！！！
static const struct meter_func_t app_task_list[] = {
    &status_task,         // 状态任务
    &fwm_upgrade_task,    // 固件升级任务
    &demand_task,         // 需量任务
#if (LC1_ENABLE || LC2_ENABLE)
    &loadcurve_task,    // 负荷曲线任务
#endif
    &billing_task,        // 计费任务
    &power_event_task,    // 电源事件任务
#if USE_RLY
    &control_task,    // 阀控任务
#endif

#if SW_PAYMENT_EN
    &step_tariff_task,    // 阶梯电价任务
    &payment_task,        // 本地预付费任务
#endif

#if USE_LCD
    &display_task,    // 显示任务
#endif

    &protocol_task,    // 协议处理任务

    &event_task,    // 事件任务
};
static const uint8_t app_task_num = sizeof(app_task_list) / sizeof(struct meter_func_t);

static const char app_identifier[32]           = APP_IDENTIFIER;
static const char app_version[VERSION_LEN_MAX] = METER_SOFT_VER;

void power_on_init(void)
{
    print_open();
    DBG_PRINTF(P_ANY, D, "\r\n%s multifunctional electric meter\r\n", METER_COMPANY);
    DBG_PRINTF(P_ANY, D, "software: %s\r\n", METER_SOFT_VER);
    DBG_PRINTF(P_ANY, T, NULL);
    DBG_PRINTF(P_ANY, D, "app start\r\n");
    DBG_PRINTF(P_ANY, D, "**********************************\r\n");

    if(hal_mcu.pwrdn_query()) { bsp.restart(0); }
    mclock.init();
    if(hal_mcu.pwrdn_query()) { bsp.restart(0); }
    tariff.init();
    if(hal_mcu.pwrdn_query()) { bsp.restart(0); }
    energy.init();
    if(hal_mcu.pwrdn_query()) { bsp.restart(0); }

    for(uint8_t i = 0; i < app_task_num; i++)
    {
        if(hal_mcu.pwrdn_query()) { bsp.restart(0); }
        if(app_task_list[i].task->init != NULL) { app_task_list[i].task->init(); }
    }
}

void idle_task_runing(void)
{
    static uint8_t idle_task = 0;

    if(idle_task >= app_task_num) idle_task = 0;
    if(app_task_list[idle_task].task->idle_run != NULL) { app_task_list[idle_task].task->idle_run(); }
    idle_task++;

#if (PUSH_METER_LASTGASP_ENABLE && HW_DCU_MODUL)
    if(hal_mcu.pwrdn_query()) { dcu.lastgasp(0); }
#endif
}

void second_task_runing(void)
{
#if TASK_PRINT_EN
    uint32_t ts = hal_timer.systick_cnt();
#endif
    mclock.refresh();
    tariff.refresh();
    energy.refresh();
    for(uint8_t i = 0; i < app_task_num; i++)
    {
        if(app_task_list[i].task->second_run != NULL) { app_task_list[i].task->second_run(); }
    }

#if TASK_PRINT_EN
    ts = hal_timer.systick_cnt() - ts;
    DBG_PRINTF(P_TASK, D, "\r\nSecond task expend %dms, Idle duty ratio %.1f%% .", ts, (1000 - ts) / 10.0);
#endif
}

/// @brief  正常上电运行
/// @param
void power_on_runing(void)
{
    SwTimer_s power_on_tmr;

    hal_timer.interval(&power_on_tmr, 1200);

    while(bsp.monitor(1))
    {
        if(hal_rtc.irq_query() || hal_timer.expired(&power_on_tmr))
        {
            hal_timer.restart(&power_on_tmr);
            /// 这里放秒任务
            second_task_runing();
        }
        /// 这里放实时循环任务
        idle_task_runing();
    }
}

/// @brief  掉电保存数据
void app_power_down_save(void)
{
    for(uint8_t i = 0; i < app_task_num; i++)
    {
        if(app_task_list[i].task->power_down_save != NULL) { app_task_list[i].task->power_down_save(); }
    }
}

/// @brief  掉电保存数据
/// @param
void power_down_ready(void)
{
    DBG_PRINTF(P_ANY, T, NULL);
    DBG_PRINTF(P_ANY, D, "\r\n >>>>>>>>>>meter power off...");

    energy.pwr_down_save();
    mclock.pwr_down_save();

    app_power_down_save();

#if (PUSH_METER_LASTGASP_ENABLE && HW_DCU_MODUL)
    if(hal_mcu.pwrdn_query()) { dcu.lastgasp(1); }
#endif
}

void power_off_init(void)
{
    mclock.init();
    energy.init();

    for(uint8_t i = 0; i < app_task_num; i++)
    {
        if(app_task_list[i].task->power_off_init != NULL) { app_task_list[i].task->power_off_init(); }
    }
}

/// @brief  辅助电源运行
/// @param
void power_off_runing(void)
{
    while(!bsp.monitor(0))
    {
        if(bsp.wakeup_state((WAKEUP_STUS)(~SECOND_WAKEUP)))
        {
            /// 非秒中断才运行
            if(bsp.wakeup_state(SECOND_WAKEUP))    // 有其它任务唤醒先获取RTC
            {
                mclock.refresh();
                bsp.wakeup_close(SECOND_WAKEUP);
            }

            for(uint8_t i = 0; i < app_task_num; i++)
            {
                if(app_task_list[i].task->power_off_run != NULL) { app_task_list[i].task->power_off_run(); }
            }
        }
    }
}

/// @brief  主函数
int main(void)
{
    bsp.init();

    while(1)
    {
        if(bsp.state_query(STUS_BSP_PWR_ON))
        {
            /// 上电初始化
            power_on_init();
            /// 上电正常运行
            power_on_runing();
            /// 掉电数据保存放这里
            power_down_ready();
            /// 重启进入低功耗模式
            bsp.restart(1);
        }
        else
        {
            /// 唤醒初始化
            power_off_init();
            /// 低功耗运行
            power_off_runing();
            /// 重启进入正常模式
            bsp.restart(0);
        }
    }
}

/// @brief  APP程序头部信息
/// @note   该结构体用于描述APP程序的基本信息，包括程序起始地址、大小、标识符、版本号、校验和等信息。
/// @note   该结构体的定义必须与bootloader中的image_header_t结构体一致，以便bootloader能够正确识别和处理APP程序。
#pragma location                              = ".app_start"
__root const struct image_header_t app_header = {
    .base_addr  = MCU_FLASH_APP_BASE,    //
    .size       = MCU_FLASH_APP_SIZE,    // 需要修改为实际长度。
    .identifier = app_identifier,
    .version    = app_version,
    .checksum   = 0,    // 填零，脚本修改填写
    .verify_tag = NULL,
};

/// @brief  app接口入口函数接口, 暂不使用
// #pragma location                = ".app_api"

// end of file
