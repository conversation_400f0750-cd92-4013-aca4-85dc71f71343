

#include "bsp.h"
#include "led.h"
#include "bsp_lcd.h"
#include "key.h"
#include "adc.h"
#include "lcd.h"
#include "debug.h"
#include "eeprom.h"
#include "ext_flash.h"
#include <string.h>
#include "beep.h"
#include "boot_cfg.h"
#include "mic.h"
#include "crc.h"

#define TEST_LCD    0
#define TEST_NVM    1
#define TEST_UART   0
#define TEST_ADC    0
#define TEST_EMU    0
#define TEST_LED    0
#define TEST_TIMER  0

extern uint32_t __checksum;
#define ADCBAT_CK_RATIO     (4 / 1)    // ADC检测电池电压变比
#if TEST_ADC
int test_adc(void)
{
    float ext_bat, inn_bat, temp;

    struct rtc_t rtc; 
    hal_rtc.time_get(&rtc);

    hal_adc.open();
    hal_timer.msdly(1000);
#ifdef ADC_CHN_EXBAT
    ext_bat = hal_adc.voltage(ADC_CHN_EXBAT);
    dprintf("\r\n\r\next_bat: %.3f V", ext_bat);
#endif
#ifdef ADC_CHN_INBAT
    inn_bat = hal_adc.voltage(ADC_CHN_INBAT);
    dprintf("\r\n\r\ninn_bat: %.3f V", inn_bat);
#endif
    temp = hal_adc.temperature();
    dprintf("\r\n\r\ntemp: %.1f", temp);
    dprintf("\r\n");

    hal_timer.msdly(1000);
#ifdef ADC_CHN_EXBAT
    ext_bat = hal_adc.voltage(ADC_CHN_EXBAT);
    dprintf("\r\n\r\next_bat: %.3f V", ext_bat);
#endif
#ifdef ADC_CHN_INBAT
    inn_bat = hal_adc.voltage(ADC_CHN_INBAT);
    dprintf("\r\n\r\ninn_bat: %.3f V", inn_bat);
#endif
    temp = hal_adc.temperature();
    dprintf("\r\n\r\ntemp: %.1f", temp);
    dprintf("\r\n");
    
    return 0;
}
#endif 

#if TEST_LCD
/// @brief 测试LCD显示
void test_lcd(void)
{
#if USE_LCD
    uint8_t i, j;
    const uint8 ch[] = "0123456789abcdefghijklmnopqrstuvwxyz";

    lcd.ctrl(LCD_PWRON_OPEN);
    dprintf("\r\nhal_lcd: full screen display for 2 seconds");
    dprintf("\r\n");
    lcd.all_light(1);
    lcd.refresh(); 
    hal_timer.msdly(2000);  HAL_WDG_RESET(); 
    
    lcd.all_light(0);
    lcd.icon_light(TYPE_ICON_P_START, 1);
    lcd.refresh();
    hal_timer.msdly(1000);
    dprintf("\r\nhal_lcd: please check the main and passive screen.");
    dprintf("\r\n");   
    for(i = 0; i < sizeof(ch); i++)
    {
        lcd.all_light(0);
        // 主副屏轮显 0123456789acdefghijklmnopqrstuvwxyz
        for(j = 0; j < LCD_MS_DIGITS; j++)
        {
            lcd.disp_string(LCD_MS, j, &ch[i], 1);
        }
#if LCD_PS_DIGITS != 0
        for(j = 0; j < LCD_PS_DIGITS; j++)
        {
            lcd.disp_string(LCD_PS, j, &ch[i], 1);
        }
#endif
        lcd.refresh();
        hal_timer.msdly(200);
        HAL_WDG_RESET();
    }
    //显示日期： 24.09.04 ---如果不对调整函数时间格式
    lcd.all_light(0);
    lcd.disp_date(24, 9, 4);
    lcd.refresh();
    hal_timer.msdly(1500); HAL_WDG_RESET();
    //显示时间： 08:35:55
    lcd.all_light(0);
    lcd.disp_time(8, 35, 55);   
    lcd.refresh();
    hal_timer.msdly(1500);HAL_WDG_RESET();
    
    {
    /// 显示BCD码
    lcd.all_light(0);
    uint16_t t_string = 0x1234;   //显示1234在主屏，右对齐
    lcd.disp_bcd(0, (uint8_t *)&t_string, 2);
#if LCD_PS_DIGITS != 0
    t_string = 0x5678;            //显示5678在副屏，右对齐
    lcd.disp_bcd(1, (uint8_t *)&t_string, 2);
#endif
    lcd.refresh();
    hal_timer.msdly(1500);HAL_WDG_RESET();
    /// 显示正数，无前导零
    lcd.all_light(0);
    lcd.disp_digit(0, 123456, LCD_MS_DIGITS, 1, 0); //显示123456在主屏，1位小数，无前导零。
    lcd.disp_digit(1, 123456, LCD_PS_DIGITS, 2, 0); //显示123456在副屏，2位小数，无前导零。
    lcd.refresh();
    hal_timer.msdly(1500);HAL_WDG_RESET();
    /// 显示正数，有前导零
    lcd.all_light(0);
    lcd.disp_digit(0, 123456, LCD_MS_DIGITS, 2, 1); //显示123456在主屏，2位小数，有前导零。
    lcd.disp_digit(1, 123456, LCD_PS_DIGITS, 3, 1); //显示123456在副屏，4位小数，有前导零。
    lcd.refresh();
    hal_timer.msdly(1500);HAL_WDG_RESET();    
    /// 显示负数，无前导零，副屏不支持显示负数
    lcd.all_light(0);  
    lcd.disp_digit(0, -123456, LCD_MS_DIGITS, 3, 0); //显示-123456在主屏，3位小数，无前导零。
    lcd.refresh();
    hal_timer.msdly(1500);HAL_WDG_RESET();
    /// 显示负数，有前导零，副屏不支持显示负数
    lcd.all_light(0);
    lcd.disp_digit(0, -123456, LCD_MS_DIGITS, 4, 1); //显示-123456在主屏，4位小数，有前导零。
    lcd.refresh();
    hal_timer.msdly(1500);HAL_WDG_RESET();
    }
    
    dprintf("\r\nhal_lcd: please check the icons");
    dprintf("\r\n");
    for(i = 0; i < TYPE_ICON_NULL; i++)
    {
        lcd.all_light(0);
        lcd.icon_light((ICON_TYPE_t)i, 1);
        lcd.refresh();
        hal_timer.msdly(300);
    }
    dprintf("\r\nhal_lcd: All seg checked!");

    //WAIT_WHILE(btn.state_get(TYPE_BTN_DISP_DN) == 0);
#else
    led.ctrl(BLACKIT_LED, FREQ_ON);
#endif
}


int test_interface(void)
{
    test_lcd();

    return 0;
}
#endif // USE_LCD

HAL_UART_TYPE print_uart = COM_RS4851;  // COM_RS4851 COM_RS4852 COM_BLE COM_IR
HAL_UART_CTRL ctrl = UC_RS485_DE; // UC_RS485_DE UC_IR_38K  UC_RS4852_DE
uint8_t test_rxbuf[200] = {0};
#if TEST_UART
/// @brief 测试串口收发
int test_uart(void)
{
    char ch_INT[] = "\n Uart interrupt send OK! Input any message in 10s to continue...\r\n";
    char ch_Poll[] = "if recv message,interrupt recv OK!\n\nPoll mode send OK! Input any interrupt in 10s to continue...\r\n";
    uint8_t len = 0, i = 0;
    //uint8_t test_rxbuf[100] = {0};

    HAL_UART_BAUDE_TYPE BAUDE = BAUDE_115200BPS; // BAUDE_9600BPS; //
    HAL_WDG_RESET();
    hal_uart.open(print_uart, ctrl, CHAR_8N1, BAUDE, test_rxbuf, 50);
    hal_timer.msdly(10);
    hal_uart.send(print_uart, ch_INT, eleof(ch_INT));
    hal_timer.msdly(500);
    while(len == 0)
    {
        len = hal_uart.recv(print_uart);
        
        hal_timer.msdly(1000);
        HAL_WDG_RESET();

        if(i++ == 10)
        {
            dprintf("\r\nhal_uart: test wait timeout exceeded!");
            dprintf("\r\n");
            return 1;
        }
    }

    for(i = 0; i < len; i++) hal_uart.print(print_uart, test_rxbuf[i]);

    hal_uart.print(print_uart, '\n');
    
    for(i = 0; i < eleof(ch_Poll); i++) hal_uart.print(print_uart, ch_Poll[i]);
    
    hal_uart.open(print_uart, ctrl, CHAR_8N1, BAUDE, NULL, NULL);  // 切换到Polling接收
    
    memset(test_rxbuf, 0, sizeof(test_rxbuf));
    len = 0,i = 0;
    while(len == 0)
    {
        len = hal_uart.scan(print_uart, test_rxbuf, 1000);
        hal_timer.msdly(1000);
        if(i++ == 10)
        {
            dprintf("\r\nhal_uart: test wait timeout exceeded!");
            dprintf("\r\n");
            return 1;
        }
    }
    for(i = 0; i < len; i++) hal_uart.print(print_uart, test_rxbuf[i]);
    hal_uart.print(print_uart, '\n');

    hal_uart.open(print_uart, ctrl, CHAR_8N1, BAUDE, test_rxbuf, 50);   // 切换回中断接收
    
    hal_uart.send(print_uart, "Poll recv OK,check completed!\r\n", 31);
    hal_timer.msdly(500);
    print_open();  //用于打印调试
    return 0;
}
#endif // TEST_UART
/// @brief 测试CODEFLASH、EEPROM、DATAFLASH存储
/// @param
#if TEST_NVM
int test_nvm(void)
{
    #define faddr    (MCU_FLASH_DATA_BASE + MCU_FLASH_DATA_SIZE - 1024)
    #define eaddr    (0 + EE_SIZE - 300)
    #define daddr    (0 + M25_SECTOR_LEN - 4096)
    int err = 0;
    
    for(uint8 cnt = 0; cnt < 3; cnt++)
    {
        uint32 ts1, ts2;
        uint8 buf[2][256];

        for(int i = 0; i < 256; i++)
        {
            buf[0][i] = i + cnt;
        }

        dprintf("\r\n\r\n\r\n>>>>>>>>>>>>>>>>hal_flash test case %d:", cnt);
        ts1 = hal_timer.systick_cnt();
        mcu_flash_w(faddr, buf[0], 256);
        ts2 = hal_timer.systick_cnt();
        memset(buf[1], 0, 256);
        mcu_flash_r(faddr,  buf[1], 256);
        if(!memcmp(buf[0], buf[1], 256))
        {
            dprintf("\r\nhal_flash.write  OK!: %dms\r\n", ts2 - ts1);
        }
        else
        {
            dprintf("\r\nhal_flash.write ERR!");
            mprintf(buf[1], 256);
            err = 1;
        }

        dprintf("\r\n>>>>>>>>>>>>>>>>eeprom test case %d:", cnt);
        ts1 = hal_timer.systick_cnt();
        eeprom.write(eaddr, buf[0], 256);
        ts2 = hal_timer.systick_cnt();
        memset(buf[1], 0, 256);
        eeprom.read(eaddr,  buf[1], 256);
        if(!memcmp(buf[0], buf[1], 256))
        {
            dprintf("\r\neeprom.write  OK! : %dms \r\n", ts2 - ts1);
        }
        else
        {
            dprintf("\r\neeprom.write ERR!");
            mprintf(buf[1], 256);
            err = 2;
        }

        dprintf("\r\n>>>>>>>>>>>>>>>>dataflash test case %d:", cnt);
        hal_gpio.data_flash(GPIO_OPEN);
        hal_spi_open(COM_DATAFLASH, 2000);
        dprintf("\r\ndataflash id: 0x%x", extflash.check());
        ts1 = hal_timer.systick_cnt();
        extflash.write(daddr, buf[0], 256);
        ts2 = hal_timer.systick_cnt();
        memset(buf[1], 0, 256);
        extflash.read(daddr,  buf[1], 256);
        if(!memcmp(buf[0], buf[1], 256))
        {
            dprintf("\r\ndataflash.write  OK!: %dms\r\n", ts2 - ts1);
        }
        else
        {
            dprintf("\r\ndataflash.write ERR!");    
            mprintf(buf[1], 256);
            err = 3;
        }

        if(err != 0) break;
    }
    
    return err; 
}
#endif
typedef struct
{
    uint16_t chk;
    uint16_t crc;
    uint32_t data;
}test_s;
#if TEST_EMU
void test_emu(void)
{
    uint16_t crc;
    test_s teststr = {12,12,0x12345678};
    mic.init(0);
    
    crc = crc16(1, (uint8_t *)&teststr + 4, sizeof(teststr) - 4);
    STRUCT_CRC16_GET(1, &teststr, sizeof(teststr)); 
    if(STRUCT_CRC16_CHK(1, &teststr, sizeof(teststr)))
    {
        dprintf("\r\nCRC OK!");
    }
    else
    {
        dprintf("\r\nCRC faile CRC right: %x!, CRC error: %x\r\n", crc, teststr.crc);
    }
    
    while(1)
    {
        mic.ins_val_refresh();
        HAL_WDG_RESET();
        hal_timer.msdly(800);
    }
}
#endif 


extern void __iar_program_start(void);
void main(void)
{
    uint32 ts ;//= __checksum;  

#if 0
    ts = BOOT_BASE_ADDR; ts++;
    if(ts)ts = BOOT_SIZE;
    if(ts)ts = BOOT_API_ADDR;

    if(ts)ts = MCU_FLASH_APP_BASE;
    if(ts)ts = APP_API_ADDR;
    if(ts)ts = MCU_FLASH_APP_SIZE;
    if(ts)ts = APP_RSTVEC_ADDR;

    if(ts)ts = BOOT_DATA_BASE;
    if(ts)ts = BOOT_DATA_SIZE;
    
    if(ts)ts = MCU_FLASH_DATA_BASE;
    if(ts)ts = MCU_FLASH_DATA_SIZE;
    
    if(ts)ts = MCU_RAM_START;
    if(ts)ts = MCU_APB_START;
#endif   
    HAL_DISABLE_INTERRUPTS(); // 关闭所有中断 
    
    hal_mcu.init();
    print_open();
    
    hal_gpio.init();
    
#if TEST_TIMER
    hal_timer.msdly(100);
    gpio_out_L(PIN_MCU_DO0);
    hal_timer.msdly(1000);
    gpio_out_H(PIN_MCU_DO0);
    hal_timer.msdly(100);
    HAL_WDG_RESET();

    gpio_out_L(PIN_MCU_DO0);
    hal_timer.xms(1000);  
    gpio_out_H(PIN_MCU_DO0);
    hal_timer.xms(100);
    gpio_out_L(PIN_MCU_DO0);
    hal_timer.xms(500);
    gpio_out_H(PIN_MCU_DO0);
    HAL_WDG_RESET();

    gpio_out_L(PIN_MCU_DO0);
    hal_mcu.wait_us(10000);
    gpio_out_H(PIN_MCU_DO0);
    hal_mcu.wait_us(1000);
    gpio_out_L(PIN_MCU_DO0);
    hal_mcu.wait_us(1000);
    gpio_out_H(PIN_MCU_DO0);

    HAL_WDG_RESET();

    gpio_out_L(PIN_MCU_DO0);
    hal_mcu.wait_us(10000);
    gpio_out_H(PIN_MCU_DO0);
    hal_mcu.wait_us(1000);
    gpio_out_L(PIN_MCU_DO0);
    hal_mcu.wait_us(10000);
    gpio_out_H(PIN_MCU_DO0);
    hal_mcu.wait_us(1000);
    gpio_out_L(PIN_MCU_DO0);

    hal_timer.systick_start(NULL); // 设置系统节拍中断调用的函数指针
    HAL_ENABLE_INTERRUPTS();
    hal_mcu.wait_us(1000);
    {
        SwTimer_s tim;
        hal_timer.interval(&tim, 1000); // 设置定时器间隔为1000ms
        gpio_out_H(PIN_MCU_DO0);
        while (1) // 替换为实际条件
        {
            if (hal_timer.expired(&tim)) // 检查定时器是否到期
            {
                break; // 退出循环或执行其他操作
            }
        }
    }
    gpio_out_L(PIN_MCU_DO0);
#endif
    HAL_WDG_RESET();

    dprintf("\r\ninit hal_mcu");
    
    LED_ACTEGY_PULSE_ON();
    LED_REAEGY_PULSE_ON();   
#if TEST_LED
    led.ctrl(BLACKIT_LED, FREQ_1HZ);
#endif
    dprintf("\r\ninit hal_gpio");  
    //__iar_program_start();
#if 1//TEST_UART
    hal_timer.systick_start(hal_uart.timer); // 设置系统节拍中断调用的函数指针
#endif
#if TEST_LCD
    lcd.ctrl(LCD_PWRON_OPEN);
    lcd.all_light(1);
    lcd.refresh();
#endif 
    // hal_timer.xms(2000);  HAL_WDG_RESET(); 
    
    dprintf("\r\nhal_timer systick start");
#if TEST_LED
    led.init();
#endif
    
    //key.init();  
    
#if USE_BUZZER
    beep.init();
#endif
    
    hal_rtc.init();
    hal_rtc.irq_set(SEC_ALARM, NULL); // RTC秒中断使能
    HAL_ENABLE_INTERRUPTS();
#if 1//TEST_TIMER
    ts = hal_timer.systick_cnt();
    hal_timer.msdly(1000);
    ts = hal_timer.systick_cnt() - ts;
    dprintf("\r\n timer ms cnt test: %d", ts);
#endif
#if USE_BUZZER
    beep.start(200, 0, 0, 0, 0); ///开机响
#endif
#if TEST_UART
    test_uart();
#endif
   // print_open();
#if TEST_LCD
    test_interface();
#endif
#if TEST_ADC
    test_adc();
#endif
#if TEST_NVM 
    test_nvm();
#endif
#if TEST_EMU
    test_emu();
#endif
#if USE_BUZZER
    beep.start(200, 0, 0, 0, 0); ///测试完
#endif
    while(!hal_mcu.pwrdn_query())
    {
        HAL_ENABLE_INTERRUPTS(); // 始终打开中断
        HAL_WDG_RESET();

        if(hal_rtc.irq_query())
        {
            struct rtc_t rtc;
            hal_rtc.time_get(&rtc);
            // tprintf();
            ts++;
        #if 0//USE_LCD
            lcd.all_light(0);
            if((ts % 10) < 5)
            {
                lcd.disp_time(rtc.hh, rtc.mm, rtc.ss);
            }
            else
            {
                lcd.disp_date(rtc.YY, rtc.MM, rtc.DD);
            }
            lcd.refresh();
        #else
            dprintf("Clock: 20%d-%d-%d,  %d:%d:%d \r\n", rtc.YY, rtc.MM, rtc.DD, rtc.hh, rtc.mm, rtc.ss);
        #endif
            //ts = hal_timer.systick_cnt();
            //dprintf("\r\n timer ms cnt test: %d", ts);
        }
    }

    HAL_DISABLE_INTERRUPTS(); // 关闭所有中断
    printf("\r\n>>>>>>>>>>>power off");
    printf("\r\n");
    while(1){}
}

