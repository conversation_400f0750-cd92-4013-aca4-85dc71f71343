
#ifndef __MODULE_PARA_H
#define __MODULE_PARA_H

#include "typedef.h"

// 各类数据长度，公有数据
#define MODULE_IP_LEN 45           // IP地址长度，IPv6最大长度
#define MODULE_HTTP_URL_LEN 128    // HTTP下载地址长度

typedef enum module_para_e
{
    MODULE_TCP = 0,           // 主IP地址
    MODULE_TCP_PORT,          // 主IP端口号
    MODULE_TCP_BAK,           // 备用IP地址
    MODULE_TCP_BAK_PORT,      // 备用IP端口号
    MODULE_NTP_SERVER,        // NTP服务器地址
    MODULE_NTP_SERVER_BAK,    // 备用NTP服务器地址
    MODULE_HTTP_URL,          // HTTP下载地址
} MODULE_PARA_t;

typedef struct ip_struct
{
    uint8_t  len;                  // IP地址长度，第一个字节为长度
    uint8_t  ip[MODULE_IP_LEN];    // IP地址内容
    uint16_t port;                 // 端口号
} ip_s;

typedef struct http_url_struct
{
    uint8_t len;                         // HTTP下载地址长度，第一个字节为长度
    uint8_t url[MODULE_HTTP_URL_LEN];    // HTTP下载地址内容
} http_url_s;

typedef struct m_para_struct
{
    uint16_t   crc;               // CRC校验码
    uint16_t   rev;               // 校验码
    ip_s       tcp;               // 主站
    ip_s       tcp_bak;           // 备用主站
    ip_s       ntp_server;        // NTP服务器
    ip_s       ntp_server_bak;    // 备用NTP服务器
    http_url_s http_url;          // HTTP下载地址
} module_para_s;

struct module_data_api_s
{
    /// @brief 重置模块参数
    /// @param type 重置类型
    void (*reset)(uint8_t type);

    /// @brief 初始化模块参数
    void (*init)(void);       

    /// @brief 获取模块参数
    /// @param buf 缓冲区指针
    /// @param typ 模块参数类型
    /// @return 返回数据长度，当为端口号时，返回uint16_t类型的端口号
    uint16_t (*para_get)(void *buf, MODULE_PARA_t typ);   

    /// @brief 设置模块参数
    /// @param buf 缓冲区指针   
    /// @param typ 模块参数类型
    /// @return 返回true表示设置成功，false表示设置失败
    bool (*para_set)(uint8_t *buf, uint16_t len, MODULE_PARA_t typ);        
};

extern const struct module_data_api_s module_para;

#endif    // __MODULE_PARA_H
