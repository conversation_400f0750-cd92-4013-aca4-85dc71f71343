/**
 ******************************************************************************
* @file    api.c
* <AUTHOR> @date    2024
* @brief   类型定义
*
******************************************************************************
*
* @note
* Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/
#include "utils.h"
#include "api.h"
#include "DLT645_2007_id.h"
#include "dispApp.h"
#include "mic.h"
#include "A_XDR.h"
#include "status.h"
#include "datastore.h"
#include "energy.h"
#include "billing.h"

#define PRODUCT_INFO_PARA   nvm_addr(NVM_PRODUCT_INFO_PARA)   /// 产品信息参数
#define PRIVATE_KEY_PARA    nvm_addr(NVM_PRIVATE_INFO)        /// 私钥参数
#define PRIVATE_KEY_CRC16           0  ///只能在测试阶段修改，产品发布后不得再修改！！！！否则会丢失数据
#define CRC16_CHK(struct,len)       STRUCT_CRC16_CHK(PRIVATE_KEY_CRC16, struct, len)
#define CRC16_CAL(struct,len)       STRUCT_CRC16_GET(PRIVATE_KEY_CRC16, struct, len)

extern const private_info_s private_info_default_para;

/// @brief 读取产品信息
/// @param tag 偏移地址 
/// @param out 输出缓冲区 
/// @return    返回数据长度+1，因为长度字节也算在内
uint16_t product_info_get(uint16_t tag, void* out)
{
    uint8_t len;
    uint8_t* ptr = (uint8_t*)out;

    switch(tag)
    {
        case PRODUCT_INFO(lock_status):          
        case PRODUCT_INFO(production_model):
        case PRODUCT_INFO(activity_model):  
        {
            nvm.read(PRODUCT_INFO_PARA + tag, ptr, 1);
            return 1;
        }
        case PRODUCT_INFO(meter_sn):         { len = 1 + METER_SN_LEN;      break; } 
        case PRODUCT_INFO(asset_code):       { len = 1 + ASSET_CODE_LEN;    break; } 
        case PRODUCT_INFO(meter_bc):         { len = 1 + BAR_CODE_LEN;      break; } 
        case PRODUCT_INFO(module_bc):        { len = 1 + BAR_CODE_LEN;      break; }
        case PRODUCT_INFO(manufacture_date): { len = 1 + METER_DATE_LEN;    break; } 
        case PRODUCT_INFO(cal_date):         { len = 1 + METER_DATE_LEN;    break; } 
        case PRODUCT_INFO(hardware_ver):     { len = 1 + HW_VER_LEN;        break; } 
        case PRODUCT_INFO(user_code):        { len = 1 + USER_CODE_LEN;     break; } 
        case PRODUCT_INFO(last_firmware_ver):{ len = 1 + VERSION_LEN_MAX;   break; } // 固件版本号，2字节
        default: return 0;
    }

    nvm.read(PRODUCT_INFO_PARA + tag, ptr, len);
    len--; // 去掉长度字节
    if(*ptr == 0 || *ptr > len)
    {
        *ptr = len;
        memset(ptr + 1, '0', len);         /// ascii码默认全‘0’
        if(tag == PRODUCT_INFO(meter_sn) || tag == PRODUCT_INFO(user_code))  /// 表号 BCD码 默认 00 00 00 00 00 01
        {
            len = METER_SN_LEN;
            memset(ptr + 1, 0x00, len);
            *ptr = len;
            *(ptr + 1) = 0x01;
        }
    }
    return (uint16_t)(len + 1);  // 返回数据长度+1，因为长度字节也算在内
}

/// @brief 设置产品信息
/// @param tag 偏移地址 
/// @param in  输入缓冲区 
/// @param len 输入数据长度 
/// @return    返回true表示成功，false表示失败
bool product_info_set(uint16_t tag, const void* in, uint16_t len)
{
    return nvm.write(PRODUCT_INFO_PARA + tag, in, len);
}

/// @brief  确认是否是工厂模式
/// @param  
/// @return  true表示是工厂模式，false表示不是
bool product_is_factory_mode(void)
{
    uint8_t factory;
    product_info_get(PRODUCT_INFO(production_model), &factory);
    if(factory != 0x5A || factory == 0xFF) return true;
    return false; 
}

/// @brief 表号获取，也作为电表通讯地址。
/// @param buf 
/// @return 
uint8_t meter_sn_get(uint8_t* buf)
{
    uint8_t sn[METER_SN_LEN + 1];
    
    product_info_get(PRODUCT_INFO(meter_sn), sn);
    memcpy(buf, &sn[1], sn[0]);
    
    return METER_SN_LEN;  // 返回表号长度
}


/// @brief 按协议解析数据帧总长度
/// @param buf 数据缓存 
/// @param len 数据包长度 
/// @return    返回数据帧总长度
uint16_t protocol_len_parse(const uint8_t *buf, uint16_t len, protocol_type_t type)
{
    uint8_t ofset = 0;
    
    switch (buf[0])
    {
        case 0xFE:
            for(; ofset < len; ofset++)
            {
                if(buf[ofset] == 0x68){ break;}
            }
        case 0x68:
            if(type == PROTOCOL_645)
            {
                if(len < (ofset + 9)) return 0;    
                // 68 6 68 c len  sum 16
                len = buf[9 + ofset] + 12 + ofset; // 数据帧总长度 = 数据域长度 + 12(帧头+校验域) + 偏移量
            }
            else if(type == PROTOCOL_698)
            {
                return 0;
            }
            else if(type == PROTOCOL_10376)
            {
                uint16_t frame_len = (buf[2 + ofset] << 8) | buf[1 + ofset]; // 获取帧长度
                if(len < (ofset + 6)) return 0;
                if(buf[1 + ofset] != buf[3 + ofset] || buf[2 + ofset] != buf[4 + ofset]) return 0; // 长度校验
                frame_len = (frame_len >> 2) & 0x3FFF; // 只保留低14位
                // 数据帧总长度 = 数据域长度 + 8((68)+(len2)+(len2)+(68)+(校验域)+(16)) + 偏移量
                len = frame_len + 8 + ofset;  
            }
            else
            {
                return 0;
            }
            break;
        default: return 0;
    }

    return len;
}

uint8_t o07_id_energy_type_get(uint32_t id, uint8_t *ph)
{
    uint8_t type    = (uint8_t)(id >> 16);
    uint8_t phase   = T_PHASE; // 分相

    if(type >= 0x3D) { type -= 0x3D; type += 1; phase = C_PHASE; }
    else if(type >= 0x29) { type -= 0x29; type += 1; phase = B_PHASE; }
    else if(type >= 0x15) { type -= 0x15; type += 1; phase = A_PHASE; }

    *ph = phase;
    switch(type)
    {
        case 0x00: return TYPE_ENERGY_ADD_ACT;  // 正向+反向有功组合电能
        case 0x01: return TYPE_ENERGY_POS_ACT;  // 正向有功电能
        case 0x02: return TYPE_ENERGY_NEG_ACT;  // 反向有功电能
// #if ENERGY_REACTIVE_ENABLE        
        case 0x03: return TYPE_ENERGY_POS_REA;  // 正向无功电能
        case 0x04: return TYPE_ENERGY_NEG_REA;  // 反向无功电能
        case 0x05: return TYPE_ENERGY_Q1_REA;   // 一象限无功电能
        case 0x06: return TYPE_ENERGY_Q2_REA;   // 二象限无功电能
        case 0x07: return TYPE_ENERGY_Q3_REA;   // 三象限无功电能
        case 0x08: return TYPE_ENERGY_Q4_REA;   // 四象限无功电能  
// #endif        
#if ENERGY_APP_ENABLE
        case 0x09: return TYPE_ENERGY_POS_APP;  // 正向视在电能
        case 0x0A: return TYPE_ENERGY_NEG_APP;  // 反向视在电能            
#endif
        default:
            *ph = 0xFF;
            return 0xFF;
    }
}

/// @brief 根据07协议ID获取需量类型
uint8_t o07_id_demand_type_get(uint32_t id, uint8_t *ph)
{
    uint8_t type    = (uint8_t)(id >> 16);
    uint8_t phase   = T_PHASE; // 分相

    if(type >= 0x3D) { type -= 0x3D; type += 1; phase = C_PHASE; }
    else if(type >= 0x29) { type -= 0x29; type += 1; phase = B_PHASE; }
    else if(type >= 0x15) { type -= 0x15; type += 1; phase = A_PHASE; }

    *ph = phase;
    switch(type)
    {
#if DEMAND_POS_ACT_ENABLE
        case 0x01: return TYPE_DEMAND_POS_ACT;  // 正向  有功
#endif
#if DEMAND_NEG_ACT_ENABLE
        case 0x02: return TYPE_DEMAND_NEG_ACT;  // 反向  有功
#endif
#if DEMAND_POS_REA_ENABLE
        case 0x03: return TYPE_DEMAND_POS_REA;  // 正向  无功
#endif        
#if DEMAND_NEG_REA_ENABLE
        case 0x04: return TYPE_DEMAND_NEG_REA;  // 反向  无功
#endif
#if DEMAND_Qx_REA_ENABLE
        case 0x05: return TYPE_DEMAND_Q1_REA;   // 一象限无功
        case 0x06: return TYPE_DEMAND_Q2_REA;   // 二象限无功
        case 0x07: return TYPE_DEMAND_Q3_REA;   // 三象限无功
        case 0x08: return TYPE_DEMAND_Q4_REA;   // 四象限无功
#endif         
#if DEMAND_POS_APP_ENABLE
        case 0x09: return TYPE_DEMAND_POS_APP;  // 正向视在  
#endif
#if DEMAND_NEG_APP_ENABLE
        case 0x0A: return TYPE_DEMAND_NEG_APP;  // 反向视在           
#endif
        default:
            *ph = 0xFF;
            return 0xFF;
    }
}

/// @brief 根据07协议ID获取电能显示数据
/// @param buf 
/// @param id 
/// @return 
static uint16_t energy_data_get(uint8_t *buf, uint32_t id)
{
    uint8_t *p_data = buf;
    uint8_t index   = (uint8_t)(id);   // 0 当前，1-12 历史
    uint8_t rate    = (uint8_t)(id >> 8);
    uint8_t type;
    uint8_t ph;  // 分相
#if SW_PAYMENT_EN 
    switch(id)
    {
        case C0_REMAIN_kWh:   

        case C0_OVERDRAFT_kWh:

        case C0_REMAIN_yuan: 

        case C0_OVERDRAFT_yuan:
            ///
            
            return 5; 
    }
#endif
    type = o07_id_energy_type_get(id, &ph);
    
    // 07协议只支持上12结算日的历史数据

    if((type == 0xFF)    || 
#if (!ENERGY_PHASE_ENABLE)
       (ph   != T_PHASE) || 
#endif
       (index  > BILLING_MONTH_LOG_NUM) || 
       ((rate > TARIFF_RATE_NUM) && (rate != 0xFF)) || 
       ((ph != T_PHASE)  && (type == TYPE_ENERGY_ADD_ACT))|| 
       ((rate == 0xFF) && (index == 0xFF))) {return 0;}

    if(rate ==0xFF)
    {
        return 0;
    }
    else if(index == 0xFF)
    {
        return 0;
    }
    else if(index == 0)
    {
        // 当前
        ENERGY_DEF_FORMAT eng; 
        eng = energy.phs_cum_value_get(ph, (ENERGY_TYPE)type, rate) / EN_SCALER_2;
        p_data += a_xdr_en_data(p_data, DT_DOUBLE_LONG, (uint32_t *)&eng);
    }
    else
    {
        // 上index结算日的历史数据
        ENERGY_DEF_FORMAT eng; 
        BL_EN_TYPE_t en_type = billing.energy_type_get(type);
        eng = billing.phs_cum_energy_get(bl_monthly, ph, index, en_type, rate) / EN_SCALER_2;
        p_data += a_xdr_en_data(p_data, DT_DOUBLE_LONG, (uint32_t *)&eng);
    }

    return (uint16_t)(p_data - buf);
}


/// @brief 显示数据获取
/// @param id   数据标识
/// @param data 数据buff，第一个字节为数据类型, 后面为数据内容
///             如果数据类型为DT_BIT_STRING(bit)\ DT_OCTET_STRING(HEX)\ DT_VISIBLE_STRING(ASCII)，则第二个字节为数据长度，后面为数据内容
///             A-XDR
/// @return 
bool disp_data_get(uint32_t id, void *data)
{
    disp_obj_api_s *api = (disp_obj_api_s *)data;
    uint8_t *data_p = (uint8_t *)api->data;  
    data_format_s format;
    int16_t  tmp16s = 0;
    int32_t  tmp32s = 0;
    uint16_t tmp16u = 0;
    uint32_t tmp32u = 0;

    api->unit     = UNIT_UNDEF;
    api->scaler   = 0;
    api->data_len = 0;
    api->sign     = 0; // 正数还是负数

    if((id & 0xFF000000) == 0x00000000)
    {
        format        = c0_data.format(id);
        api->unit     = format.unit;
        api->sign     = format.sign;
        api->scaler   = EN_SCALER_2;
        api->data_len = energy_data_get(data_p, id);
    }
    else if((id & 0xFF000000) == 0x01000000)
    {

    }
    else if((id & 0xFF000000) == 0x02000000)
    {
        switch(id)
        {
            case C2_A_VOL          :    // @0x02010100  /// A相电压
                tmp16u        = (uint16_t)(mic.ins->vrms[0] * 10);
                api->unit     = UNIT_V;
                api->scaler   = -1;
                api->data_len = a_xdr_en_data(data_p, DT_LONG_UNSIGNED, &tmp16u);
                break;
            #if defined(POLYPHASE_METER)
            case C2_B_VOL          :    // @0x02010200  /// B相电压
                tmp16u = (uint16_t)(mic.ins->vrms[1] * 10);
                api->unit     = UNIT_V;
                api->scaler   = -1;
                api->data_len = a_xdr_en_data(data_p, DT_LONG_UNSIGNED, &tmp16u);
                break;
            case C2_C_VOL          :    // @0x02010300  /// C相电压
                tmp16u        = (uint16_t)(mic.ins->vrms[2] * 10);
                api->unit     = UNIT_V;
                api->scaler   = -1;
                api->data_len = a_xdr_en_data(data_p, DT_LONG_UNSIGNED, &tmp16u);
                break;
            #endif
            case C2_A_CUR          :    // @0x02020100  /// A相    电流
                tmp16s        = (uint16_t)(mic.ins->irms[0] * 100);
                api->unit     = UNIT_A;
                api->scaler   = -2;
                api->data_len = a_xdr_en_data(data_p, DT_LONG, &tmp16s);
                break;
            #if defined(POLYPHASE_METER)
            case C2_B_CUR          :    // @0x02020200  /// B相   电流
                tmp16s        = (uint16_t)(mic.ins->irms[1] * 100);
                api->unit     = UNIT_A;
                api->scaler   = -2;
                api->data_len = a_xdr_en_data(data_p, DT_LONG, &tmp16s);
                break;
            case C2_C_CUR          :    // @0x02020300  /// C相   电流
                tmp16s        = (uint16_t)(mic.ins->irms[2] * 100);
                api->unit     = UNIT_A;
                api->scaler   = -2;
                api->data_len = a_xdr_en_data(data_p, DT_LONG, &tmp16s);
                break;
            #endif
            case C2_T_INS_P        :    // @0x02030000  /// 总    有功功率
                tmp32s        = (uint16_t)(mic.ins->pwr_p[0] * 10000);
                api->unit     = UNIT_kW;
                api->scaler   = -4;
                api->data_len = a_xdr_en_data(data_p, DT_DOUBLE_LONG, &tmp32s);
            case C2_A_INS_P        :    // @0x02030100  /// A相   有功功率
                tmp32s        = (uint16_t)(mic.ins->pwr_p[1] * 10000);
                api->unit     = UNIT_kW;
                api->scaler   = -4;
                api->data_len = a_xdr_en_data(data_p, DT_DOUBLE_LONG, &tmp32s);
                break;
            #if defined(POLYPHASE_METER)
            case C2_B_INS_P        :    // @0x02030200  /// B相   有功功率
                tmp32s        = (uint16_t)(mic.ins->pwr_p[2] * 10000);
                api->unit     = UNIT_kW;
                api->scaler   = -4;
                api->data_len = a_xdr_en_data(data_p, DT_DOUBLE_LONG, &tmp32s);
                break;
            case C2_C_INS_P        :    // @0x02030300  /// C相   有功功率
                tmp32s        = (uint16_t)(mic.ins->pwr_p[3] * 10000);
                api->unit     = UNIT_kW;
                api->scaler   = -4;
                api->data_len = a_xdr_en_data(data_p, DT_DOUBLE_LONG, &tmp32s);
                break;
            #endif
            case C2_T_INS_Q        :    // @0x02040000  /// 总    无功功率
                tmp32s        = (uint16_t)(mic.ins->pwr_q[0] * 10000);
                api->unit     = UNIT_kvar;
                api->scaler   = -4;
                api->data_len = a_xdr_en_data(data_p, DT_DOUBLE_LONG, &tmp32s);
                break;
            case C2_A_INS_Q        :    // @0x02040100  /// A相   无功功率
                tmp32s        = (uint16_t)(mic.ins->pwr_q[1] * 10000);
                api->unit     = UNIT_kvar;
                api->scaler   = -4;
                api->data_len = a_xdr_en_data(data_p, DT_DOUBLE_LONG, &tmp32s);
                break;
            #if defined(POLYPHASE_METER)
            case C2_B_INS_Q        :    // @0x02040200  /// B相   无功功率
                tmp32s        = (uint16_t)(mic.ins->pwr_q[2] * 10000);
                api->unit     = UNIT_kvar;
                api->scaler   = -4;
                api->data_len = a_xdr_en_data(data_p, DT_DOUBLE_LONG, &tmp32s);
                break;
            case C2_C_INS_Q        :    // @0x02040300  /// C相   无功功率
                tmp32s        = (uint16_t)(mic.ins->pwr_q[3] * 10000);
                api->unit     = UNIT_kvar;
                api->scaler   = -4;
                api->data_len = a_xdr_en_data(data_p, DT_DOUBLE_LONG, &tmp32s);
                break;
            #endif
            case C2_T_INS_S        :    // @0x02050000  /// 总    视在功率
                tmp32s        = (uint16_t)(mic.ins->pwr_s[0] * 10000);
                api->unit     = UNIT_kVA;
                api->scaler   = -4;
                api->data_len = a_xdr_en_data(data_p, DT_DOUBLE_LONG, &tmp32s);
                break;
            case C2_A_INS_S        :    // @0x02050100  /// A相   视在功率
                tmp32s        = (uint16_t)(mic.ins->pwr_s[1] * 10000);
                api->unit     = UNIT_kVA;
                api->scaler   = -4;
                api->data_len = a_xdr_en_data(data_p, DT_DOUBLE_LONG, &tmp32s);
                break;
            #if defined(POLYPHASE_METER)
            case C2_B_INS_S        :    // @0x02050200  /// B相   视在功率
                tmp32s        = (uint16_t)(mic.ins->pwr_s[2] * 10000);
                api->unit     = UNIT_kVA;
                api->scaler   = -4;
                api->data_len = a_xdr_en_data(data_p, DT_DOUBLE_LONG, &tmp32s);
                break;
            case C2_C_INS_S        :    // @0x02050300  /// C相   视在功率
                tmp32s        = (uint16_t)(mic.ins->pwr_s[3] * 10000);
                api->unit     = UNIT_kVA;
                api->scaler   = -4;
                api->data_len = a_xdr_en_data(data_p, DT_DOUBLE_LONG, &tmp32s);
                break;
            #endif
            case C2_T_PF           :    // @0x02060000  /// 总    功率因素
                tmp16u        = (uint16_t)(mic.ins->pf[0] * 1000);
                api->unit     = UNIT_UNDEF;
                api->scaler   = -3;
                api->data_len = a_xdr_en_data(data_p, DT_LONG_UNSIGNED, &tmp16u);
                break;
            case C2_A_PF           :    // @0x02060100  /// A相   功率因素
                tmp16u        = (uint16_t)(mic.ins->pf[1] * 1000);
                api->unit     = UNIT_UNDEF;
                api->scaler   = -3;
                api->data_len = a_xdr_en_data(data_p, DT_LONG_UNSIGNED, &tmp16u);
                break;
            #if defined(POLYPHASE_METER)
            case C2_B_PF           :    // @0x02060200  /// B相   功率因素
                tmp16u        = (uint16_t)(mic.ins->pf[2] * 1000);
                api->unit     = UNIT_UNDEF;
                api->scaler   = -3;
                api->data_len = a_xdr_en_data(data_p, DT_LONG_UNSIGNED, &tmp16u);
                break;
            case C2_C_PF           :    // @0x02060300  /// C相   功率因素
                tmp16u        = (uint16_t)(mic.ins->pf[3] * 1000);
                api->unit     = UNIT_UNDEF;
                api->scaler   = -3;
                api->data_len = a_xdr_en_data(data_p, DT_LONG_UNSIGNED, &tmp16u);
                break;
            #endif
            case C2_A_ANGEL        :    // @0x02070100  /// A相   相角
                tmp16u        = (uint16_t)(mic.ins->vi_angle[0] * 10);
                api->unit     = UNIT_ANGLE;
                api->scaler   = -1;
                api->data_len = a_xdr_en_data(data_p, DT_LONG_UNSIGNED, &tmp16u);
                break;
            #if defined(POLYPHASE_METER)
            case C2_B_ANGEL        :    // @0x02070200  /// B相   相角
                tmp16u        = (uint16_t)(mic.ins->vi_angle[1] * 10);
                api->unit     = UNIT_ANGLE;
                api->scaler   = -1;
                api->data_len = a_xdr_en_data(data_p, DT_LONG_UNSIGNED, &tmp16u);
                break;
            case C2_C_ANGEL        :    // @0x02070300  /// C相   相角
                tmp16u        = (uint16_t)(mic.ins->vi_angle[2] * 10);
                api->unit     = UNIT_ANGLE;
                api->scaler   = -1;
                api->data_len = a_xdr_en_data(data_p, DT_LONG_UNSIGNED, &tmp16u);
                break;
            #endif
            case C2_N_CUR          :    // @0x02800001  /// 零线电流
                tmp16u        = (uint16_t)(mic.ins->n_irms * 1000);
                api->unit     = UNIT_A;
                api->scaler   = -3;
                api->data_len = a_xdr_en_data(data_p, DT_LONG_UNSIGNED, &tmp16u);
                break;
            case C2_FREQUENVE      :    // @0x02800002  /// 电网频率
                tmp16u        = (uint16_t)(mic.ins->freq * 100);
                api->unit     = UNIT_Hz;
                api->scaler   = -2;
                api->data_len = a_xdr_en_data(data_p, DT_LONG_UNSIGNED, &tmp16u);
                break;
            case C2_AVR_POWER_1M   :    // @0x02800003  /// 一分钟有功平均功率
                break;
            case C2_CUR_DM_kW      :    // @0x02800004  /// 当前有功需量
                break;
            case C2_CUR_DM_kva     :    // @0x02800005  /// 当前无功需量
                break;
            case C2_CUR_DM_kV      :    // @0x02800006  /// 当前视在需量
                break;
            case C2_TEMPARETURE    :    // @0x02800007  /// 温度
                tmp16s        = mstatus.temperature_get();
                api->unit     = UNIT_TEMP;
                api->scaler   = -1;
                api->data_len = a_xdr_en_data(data_p, DT_LONG, &tmp16s);
                break;
            case C2_INTBAT_VOL     :    // @0x02800008  /// 内部时钟电池电压
                tmp16u        = (uint16_t)(mstatus.battery_voltage_get(1)/10);
                api->unit     = UNIT_V;
                api->scaler   = -1;
                api->data_len = a_xdr_en_data(data_p, DT_LONG_UNSIGNED, &tmp16u);
                break;
            case C2_EXTBAT_VOL     :    // @0x02800009  /// 外部抄表电池电压
                tmp16u        = (uint16_t)(mstatus.battery_voltage_get(0)/10);
                api->unit     = UNIT_V;
                api->scaler   = -1;
                api->data_len = a_xdr_en_data(data_p, DT_LONG_UNSIGNED, &tmp16u);
                break;
            case C2_INTBAT_RUN_TIME:    // @0x0280000A  /// 内部电池工作时间
                break;
            case C2_CUR_STEP_PRICE :    // @0x0280000B  /// 当前阶梯电价
                break;
            default:
                return false;
        }
        return true;
    }
    else if((id & 0xFF000000) == 0x04000000)
    {
        switch(id)
        {
            case C4_DATE_WEEK:
                api->unit     = UNIT_UNDEF;
                api->scaler   = 0;
                api->data_len = 5;
                *data_p++ = DT_OCTET_STRING; 
                *data_p++ = 3;
                *data_p++ = mclock.datetime->year;
                *data_p++ = mclock.datetime->month;
                *data_p++ = mclock.datetime->day;
                break;
            case C4_TIME:
                api->unit     = UNIT_UNDEF;
                api->scaler   = 0;
                api->data_len = 5;
                *data_p++ = DT_OCTET_STRING;
                *data_p++ = 3;
                *data_p++ = mclock.datetime->hour;
                *data_p++ = mclock.datetime->minute;
                *data_p++ = mclock.datetime->second;
                break;
            case C4_COMM_ADDR:
            case C4_METER_NO:
                api->unit     = UNIT_UNDEF;
                api->scaler   = 0;
                *data_p++ = DT_OCTET_STRING;
                api->data_len = product_info_get(PRODUCT_INFO(meter_sn), data_p) + 1;
                string_reverse(data_p + 1, api->data_len - 2); //倒序
                break;
            default:
                return false;
        }
    }   
    return true;
}




/// @brief 645密码比对
/// @param buf  密码数据
/// @return 密码等级 0-9级密码，0xFF表示密码错误
uint8_t dlt645_password_query(uint8_t *buf)
{
    const private_info_s *info = (const private_info_s *)PRIVATE_KEY_PARA;
    if(CRC16_CHK(info, sizeof(private_info_s)) == false)
    {
        info = &private_info_default_para;
    }

    if(buf[0] >= PASSWORD_NUM)
    {
        if(buf[0] == 0x99) { return 0x99; }       //明文+MAC
        else if(buf[0] == 0x98) { return 0x98; }  //密文+MAC
        else return 0xFF;  //没有匹配的密码
    }

    if(memcmp(buf + 1, info->password[buf[0]], 3) == 0) { return buf[0]; }

    return 0xFF;  //没有匹配的密码
}

/// @brief 设置645密码
/// @param buf 密码数据
/// @param tye 密码类型
/// @return 成功返回true，失败返回false
bool dlt645_password_set(uint8_t *buf)
{
    private_info_s info;

    if(*buf >= PASSWORD_NUM) { return false; }
    memcpy (info.password[*buf], buf + 1, 3);
    CRC16_CAL(&info, sizeof(private_info_s));
    return nvm.write(PRIVATE_KEY_PARA, &info, sizeof(private_info_s));
}


const struct api_s api = 
{
    .disp_data_get          = disp_data_get,
    .product_info_get       = product_info_get,
    .product_info_set       = product_info_set,
    .meter_sn_get           = meter_sn_get,
    .protocol_len_parse     = protocol_len_parse,
    .dlt645_password_query  = dlt645_password_query,
    .dlt645_password_set    = dlt645_password_set,
    .is_factory_mode        = product_is_factory_mode,
};

