
/**
 ******************************************************************************
 * @file    image_transfer_api.c
 * <AUTHOR> @date    2024
 * @brief   处理升级相关的API接口
 *
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2024 JiSHe Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/

#include "image_transfer_api.h"
#include "datastore.h"
#include "boot_api.h"
#include "crc.h"
#include "app.h"
#include "energy.h"
#include "status.h"
#include "api.h"

#define FMW_UPGRADE_ADDR nvm_addr(NVM_FMW_UPGRADE_DATA)
#define FMW_DOWNLOAD_ADDR nvm_addr(NVM_FIRMWARE_DOWNLOAD)

static uint32_t               active_Ymodem = 0;
static image_transfer_satus_t transfer_status;           // 升级状态机
static FMW_STATE              fmw_out_stus;              // 事件记录输出状态字
static uint32_t               crc32_val;                 // 文件CRC32校验值
uint16_t                      IMAGE_BLOCK_SIZE = 256;    // 默认IMAGE传输块大小256

/// @brief 初始化IMAGE传输块大小
/// @param apdu_size 通讯协议数据域大小。
void image_block_size_init(uint16_t apdu_size)
{
    IMAGE_BLOCK_SIZE = (apdu_size > IMAGE_BLOCK_MAX_SIZE) ? IMAGE_BLOCK_MAX_SIZE : apdu_size;
}

/// @brief 获取数据块大小
uint16_t image_block_size_get(void)
{
    return IMAGE_BLOCK_SIZE;
}

/// @brief 获取当前传输的文件块状态
/// @param block_status 指向存储文件块状态的缓冲区
/// @return 返回总文件块数
uint16_t image_transferred_block_status_get(uint8_t *block_status)
{
    uint16_t len;
    uint32_t image_size = boot_information->image_size;

    if(image_size > IMAGE_TOTAL_SIZE || image_size == 0) return 0;

    len = 1 + (image_size - 1) / IMAGE_BLOCK_SIZE;
    nvm.read(FMW_UPGRADE_ADDR + member_offset(image_info_s, block_status), block_status, (len + 7) / 8);
    return len;
}

/// @brief 获取第一个未传输的数据块
/// @return 返回第一个未传输的块号，如果所有块都已传输则返回0xFFFFFFFF
/// 如果未传输块号超过总块数，则返回0。
uint32_t image_first_not_transferred_block_number(void)
{
    uint16_t idx    = 0;
    uint16_t number = 0;
    uint16_t total_bits_num;
    uint8_t  block_status[IMAGE_BLOCK_MAX_NUM / 8];

    total_bits_num = image_transferred_block_status_get(block_status);
    if(total_bits_num == 0) return 0;
    while(1)
    {
        for(uint8_t i = 1; i != 0; i <<= 1)
        {
            if((block_status[idx] & i) == 0) return number;
            if(++number >= total_bits_num) return 0xFFFFFFFF;
        }
        idx++;
    }
}

/// @brief 获取程序传输使能状态
/// @note 该函数用于检查程序传输是否被允许。
/// @param
/// @return 返回true表示传输使能，false表示禁止传输。
bool image_transfer_enabled_get(void)
{
    uint8_t enabled;
    nvm.read(FMW_UPGRADE_ADDR + member_offset(image_info_s, enabled), &enabled, 1);
    return boolof(enabled != 0);
}

/// @brief 设置程序传输使能状态
/// @param enabled 传输使能状态，0表示禁止传输，1表示允许传输。
/// @return 返回true表示设置成功，false表示设置失败。
bool image_transfer_enabled_set(uint8_t enabled)
{
    nvm.write(FMW_UPGRADE_ADDR + member_offset(image_info_s, enabled), &enabled, 1);
    return true;
}

/// @brief 获取当前的传输状态
/// @return 返回当前的传输状态，类型为image_transfer_satus_t。
image_transfer_satus_t image_transfer_status_get(void)
{
    if(transfer_status == activation_temp_initiated) return activation_initiated;
    return transfer_status;
}

/// @brief 设置当前的传输状态
/// @param t_status 要设置的传输状态，类型为image_transfer_satus_t
void image_transfer_status_set(image_transfer_satus_t t_status)
{
    transfer_status = t_status;
    nvm.write(FMW_UPGRADE_ADDR + member_offset(image_info_s, transfer_status), &t_status, 1);
}

/// @brief 获取要激活的镜像信息
/// @note 该函数用于获取当前镜像的大小、标识符和签名信息。
/// @param info 指向image_info_s结构体的指针，用于存储镜像信息。
void image_to_activate_info_get(image_info_s *info)
{
    if(transfer_status == verification_successful)
    {
        info->image_size = boot_information->image_size;
        memcpy(&info->image_identifier, boot_information->image_identifier, IDENTIFIER_MAX_LEN);    /// 第一个字节为固件标识符长度
        memcpy(&info->image_signature, boot_information->image_signature, SIGNATURE_MAX_LEN);       /// 第一个字节为签名长度
    }
    else
    {
        info->image_size          = 0;
        info->image_identifier[0] = 0;
        info->image_signature[0]  = 0;
    }
}

/// @brief 初始化传输文件时所需的数据
/// @param image_size
/// @param identifier
/// @return
bool image_transfer_initiate(uint32_t image_size, const uint8_t *identifier)
{
    uint8_t            block_status[IMAGE_BLOCK_MAX_NUM / 8];
    struct boot_info_t info;

    memset(&info, 0, sizeof(struct boot_info_t));

    /// 验证输入的标识符，放在app起始地址处
    if(identifier[0] >= IDENTIFIER_MAX_LEN) return false;
    if(memcmp(&identifier[1], app_header.identifier, strlen(app_header.identifier))) return false;
    memcpy(info.image_identifier, identifier, identifier[0] + 1);

    /// 验证输入的程序大小
    if(image_size < SIGNATURE_MAX_LEN || image_size > IMAGE_TOTAL_SIZE) return false;
    info.image_size = image_size;

    /// 初始化文件传输块状态
    memset(block_status, 0, IMAGE_BLOCK_MAX_NUM / 8);
    nvm.write(FMW_UPGRADE_ADDR + member_offset(image_info_s, block_status), block_status, (IMAGE_BLOCK_MAX_NUM / 8));

    /// 保存信息到BOOT信息域
    mcu_flash_w((uint32_t)boot_information, &info, sizeof(struct boot_info_t));

    /// 设置传输状态为已初始化
    image_transfer_status_set(transfer_initiated);

    return true;
}

/// @brief 传输一个数据块
/// @param block_number 要传输的块号，从0开始计数
/// @param image 传输文件的指针，指向获取的传输数据块
/// @param len 数据块长度
/// @return 返回true表示传输成功，false表示传输失败或传输状态未初始化。
bool image_block_transfer(uint32_t block_number, uint8_t *image, uint16_t len)
{
    uint16_t total_bits_num;
    uint8_t  block_status_byte;
    uint32_t block_status_addr = FMW_UPGRADE_ADDR + member_offset(image_info_s, block_status);
    uint32_t download_addr     = FMW_DOWNLOAD_ADDR;

    /// 判断传输状态是否已初始化
    if(transfer_status != transfer_initiated) return false;

    /// 判断block_number 是否已传输
    block_status_addr += block_number / 8;
    nvm.read(block_status_addr, &block_status_byte, 1);
    if(block_status_byte & (1 << (block_number % 8))) return true;

    /// 判断块传输号及大小是否合法
    total_bits_num = 1 + (boot_information->image_size - 1) / IMAGE_BLOCK_SIZE;
    if(block_number >= total_bits_num) return false;
    if(len > IMAGE_BLOCK_SIZE) return false;
    if(block_number + 1 != total_bits_num && len != IMAGE_BLOCK_SIZE) return false;    // 块长度不符，不是最后一块，长度应为IMAGE_BLOCK_SIZE

    /// 对比block number把明文存入到对应的下载位置
    download_addr += block_number * IMAGE_BLOCK_SIZE;
    if(!nvm.write(download_addr, image, len)) { return false; }

    /// 置位相应的BLOCK STATUS BIT, 并写入NVM
    block_status_byte |= 1 << (block_number % 8);
    nvm.write(block_status_addr, &block_status_byte, 1);

    /// 如果是最后一个块，则读取签名并保存到BOOT信息域
    if(block_number + 1 == total_bits_num)
    {
        struct boot_info_t info;
        memcpy(&info, boot_information, sizeof(struct boot_info_t));
        nvm.read(download_addr + len - SIGNATURE_MAX_LEN, info.image_signature, SIGNATURE_MAX_LEN);
        mcu_flash_w((uint32_t)boot_information, &info, sizeof(struct boot_info_t));    /// 保存到BOOT信息域
    }

    return true;
}

/// @param image_size 升级文件大小
/// @param buff json格式升级信息buff
/// @return 返回true表示初始化成功，false表示初始化失败。
bool fwm_upgrade_http_init(uint32_t image_size, const char *buff)
{
    /// 1. 判断程序标识
    if(strstr(buff, app_header.identifier) == NULL) return false;

    /// 2. 判断文件大小
    if(image_size < SIGNATURE_MAX_LEN || image_size > IMAGE_TOTAL_SIZE) return false;

    /// 3. 判断版本号

    return true;
}

/// @brief HTTP传输数据块存储
/// @note 该函数用于将传输的数据块存储到指定的地址。如果输入长度为0，则表示传输结束。返回0表示传输过程错误.
/// @param block_len 数据块长度
/// @param block_number 数据块编号
/// @param image 文件数据指针
/// @param len 数据长度
bool fwm_upgrade_http_transfer(uint16_t block_len, uint32_t block_number, uint8_t *image, uint16_t len)
{
    if(len != 0)
    {
        /// 对比block number把明文存入到对应的下载位置
        if(nvm.write(FMW_DOWNLOAD_ADDR + (block_number * block_len), image, len) == false) { return false; } // 如果写入失败，返回false
        if(transfer_status != verification_failed)
        {
            image_transfer_status_set(verification_failed);    // 如果之前是校验失败状态，则重新置位为校验准备状态
        }
    }
    else
    {
        image_transfer_status_set(verification_initiated);    // 置位为校验准备状态
    }
    return true;
}

/// @brief 升级程序数据的校验初始化
bool image_verify(void)
{
    /// 判断之前是否初始化过
    if(transfer_status != transfer_initiated) return false;

    /// 判断程序是否所有块收完
    if(image_first_not_transferred_block_number() != 0xFFFFFFFF) return false;

    /// 置位正处于待校验状态
    image_transfer_status_set(verification_initiated);

    return true;
}

/// @brief 升级程序的激活初始化
bool image_activate(void)
{
    /// 判断是否校验成功
    if(transfer_status != verification_successful) return false;

    /// 置位正处于待激活状态
    image_transfer_status_set(activation_initiated);

    return true;
}

// @note 该函数用于验证文件初始化
static void image_signature_init(void)
{
    crc32_val = CRC32_START_VALUE;
}
// @brief 计算文件校验
/// @param buf 指向要计算校验的缓冲区
/// @param len 缓冲区长度
static void image_signature_calc(uint8_t *buf, uint32_t len)
{
    crc32_val = crc32(crc32_val, buf, len);
}

/// @brief 比较校验
/// @note 该函数用于比较文件签名是否正确。
/// @return 返回true表示签名验证成功，false表示验证失败。
static bool image_signature_compare(void)
{
    return !memcmp(&boot_information->image_signature[1], &crc32_val, 4);
}

/// @brief 升级文件校验
/// @param
void fwm_upgrade_verify(void)
{
    switch(transfer_status)
    {
        case verification_initiated: {
            static uint32_t ofst     = 0xffffffff;
            static uint32_t left_len = 0;
            if(ofst == 0xffffffff)
            {
                ofst     = 0;
                left_len = boot_information->image_size - SIGNATURE_MAX_LEN;
                image_signature_init();
            }
            else if(left_len != 0)
            {
                uint8_t  buf[1024];
                uint32_t len = (left_len >= sizeof(buf)) ? sizeof(buf) : left_len;
                nvm.read(FMW_DOWNLOAD_ADDR + ofst, buf, len);
                ofst += len, left_len -= len;
                image_signature_calc(buf, len);
            }
            else
            {
                image_transfer_status_set(image_signature_compare() ? verification_successful : verification_failed);
                if(transfer_status == verification_successful) { fmw_out_stus |= APP_FMW_VERIFIED; }
                else if(transfer_status == verification_failed) { fmw_out_stus |= APP_FMW_VERIFY_FAILURE; }
            }
            break;
        }

        case verification_successful: {
            image_transfer_status_set(activation_initiated);
            break;
        }
        case activation_temp_initiated:
        case activation_initiated: {
            static SwTimer_s   t = {0, 0};
            struct boot_info_t info;

            if(t.interval == 0)
            {
                hal_timer.interval(&t, 3000);
                image_transfer_status_set(activation_temp_initiated);
            }
            if(hal_timer.expired(&t)) { t.interval = 0; }
            else { return; }

            /// @note 该函数用于将升级信息写入BOOT信息域，并设置升级模式。
            memcpy(&info, boot_information, sizeof(struct boot_info_t));
            info.mode = MODE_RUNING_IAP;
            mcu_flash_w((uint32_t)boot_information, &info, sizeof(struct boot_info_t));
            nvm.write(FMW_UPGRADE_ADDR + member_offset(image_info_s, app_last_version), app_header.version, VERSION_LEN_MAX);    /// 保存当前软件版本号
            image_transfer_status_set(activation_initiated);

            // 升级前任务处理
            if(!memcmp(&boot_information->image_identifier[1], app_header.identifier, strlen(app_header.identifier)))
            {
                energy.pwr_down_save();    // 升级前电能保存
                app_power_down_save();
                bsp.restart(0);    // 系统复位，运行Bootloader
            }
            break;
        }
    }

    if(active_Ymodem == 0x55AA6699)
    {
        static SwTimer_s t2 = {0, 0};
        // 等待通讯完毕
        if(t2.interval == 0) { hal_timer.interval(&t2, 1000); }
        if(hal_timer.expired(&t2)) { t2.interval = 0; }
        else { return; }

        active_Ymodem = 0;
        // 升级前任务处理
        energy.pwr_down_save();    // 升级前电能保存
        app_power_down_save();
        // 重启进入ISP模式
        bsp.restart(0);    // 系统复位，运行Bootloader
    }
}

/// @brief 程序上电初始化时的固件升级激活状态检测
void fwm_upgrade_init(void)
{
    uint32_t addr = FMW_UPGRADE_ADDR;

    /// 获取当前升级状态
    nvm.read(addr + member_offset(image_info_s, transfer_status), &transfer_status, 1);
    if(transfer_status != activation_initiated) return;

    /// 查询是否有过升级
    if(boot_information->mode == MODE_RUNING_APP)
    {
        char app_last_version[1 + VERSION_LEN_MAX];

        /// 保存程序上一版本号
        nvm.read(addr + member_offset(image_info_s, app_last_version), app_last_version + 1, VERSION_LEN_MAX);
        app_last_version[0] = strlen(app_last_version + 1) > VERSION_LEN_MAX ? VERSION_LEN_MAX : strlen(app_last_version + 1);    // 版本号长度
        api.product_info_set(PRODUCT_INFO(last_firmware_ver), &app_last_version, 2);

        /// 置位记录升级成功事件标志
        image_transfer_status_set(activation_successful);
        fmw_out_stus |= APP_FMW_UPGRADE;
    }
    else
    {
        /// 置位记录升级失败事件标志
        image_transfer_status_set(activation_failed);
    }
}

void fwm_upgrade_activate_isp(void)
{
    struct boot_info_t info = {0};

    // 设置升级模式为ISP
    info.mode = MODE_RUNING_ISP;
    mcu_flash_w((uint32_t)boot_information, &info, sizeof(struct boot_info_t));

    // 使能Ymodem升级
    active_Ymodem = 0x55AA6699;
}

/// @brief 获取固件升级事件状态
bool fwm_upgrade_state_query(FMW_STATE state)
{
    return boolof(fmw_out_stus & state);
}

/// @note 该函数用于设置固件升级事件状态。
/// @param state 要设置的状态，类型为FMW_STATE。
void FMW_StateSet(FMW_STATE state)
{
    fmw_out_stus |= state;
}

/// @brief 清除固件升级事件状态
void fwm_upgrade_state_clr(void)
{
    fmw_out_stus = 0;
}

/// @brief 获取固件校验
/// @param fwm_type
/// @return 返回当前APP的crc32校验值
uint32_t fwm_upgrade_crc32_get(void)
{
    return app_header.checksum;    /// 获取当前APP的校验值
}

/// @brief 声明类18：image_transfer接口
const struct image_transfer_s image = {
    .block_size_get                     = image_block_size_get,
    .transferred_block_status_get       = image_transferred_block_status_get, 
    .first_not_transferred_block_number = image_first_not_transferred_block_number,
    .transfer_enabled_get               = image_transfer_enabled_get,
    .transfer_enabled_set               = image_transfer_enabled_set,
    .transfer_status_get                = image_transfer_status_get,
    .transfer_status_set                = image_transfer_status_set,
    .to_activate_info_get               = image_to_activate_info_get,
    .transfer_initiate                  = image_transfer_initiate,
    .block_transfer                     = image_block_transfer,
    .verify                             = image_verify,
    .activate                           = image_activate,
    .block_size_init                    = image_block_size_init,
};

/// @brief 声明类18：fwm_upgrade接口
const struct fwm_upgrade_s fwm_upgrade = {
    .activate_isp  = fwm_upgrade_activate_isp,
    .state_query   = fwm_upgrade_state_query,
    .state_clr     = fwm_upgrade_state_clr,
    .crc32_get     = fwm_upgrade_crc32_get,
    .http_init     = fwm_upgrade_http_init,
    .http_transfer = fwm_upgrade_http_transfer,
};

/// @brief 声明升级模块任务接口
const struct app_task_t fwm_upgrade_task = {
    .init     = fwm_upgrade_init,
    .idle_run = fwm_upgrade_verify,
};
