﻿
@echo off
@setlocal ENABLEDELAYEDEXPANSION
chcp 65001 >nul

:: 文件输出目录
@set des_dir=F:\Project

::保存当前工作目录
pushd %~dp0 & for %%i in (.) do set "target=%%~nxi"

::去到项目根目录
cd ..\

::保存当前项目路径
@set currDir=%CD%

::将项目名记录下来
for %%i in ("%currDir%") do set "projectName=%%~ni"

:: 将版本信息记录下来
for /R "%currDir%\output" %%i in (*.hex) do (
  set "filePath=%%~i"
  for /F "tokens=1,2 delims=()" %%A in ("%%~ni") do (
    if "%%B" neq "" (
	  for /F "tokens=1,2 delims=-" %%a in ("%%B") do set "versionPart=%%a-%%b"
    )
  )
)



::判断是否存在项目文件夹,不存在则创建
if not exist "%des_dir%\%projectName%\%versionPart%" 	mkdir "%des_dir%\%projectName%\%versionPart%"

:: 复制output的文件
if exist "%des_dir%\%projectName%\%versionPart%" (
	xcopy "%currDir%\output" "%des_dir%\%projectName%\%versionPart%" /E /I /Y
	echo jenkins_file_copy: Ok!
) else (
	goto ERROR
)

:: 将document文件夹中的提测文件复制到输出路径
@set source_dir=%currDir%\document

if not exist "%des_dir%\%projectName%\%versionPart%\提测文件" mkdir "%des_dir%\%projectName%\%versionPart%\提测文件"

for /R "%source_dir%" %%F in (*改动说明* *手册* *自测报告* *参数配置表* *指引*) do (
    if exist "%%F" (
        move "%%F" "%des_dir%\%projectName%\%versionPart%\提测文件"
		echo jenkins_file_move: Ok!
    )
)

goto END

:ERROR
ECHO  jenkins_file_copy.bat: Error!
exit /b 1

:END
exit /b 0