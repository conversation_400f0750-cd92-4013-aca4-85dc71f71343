/**
 ******************************************************************************
 * @file    app_config.h
 * <AUTHOR> @date    2024
 * @brief   APP 功能配置和功能参数配置
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/
#ifndef __APP_CONFIG_H__
#define __APP_CONFIG_H__

#include "bsp_cfg.h"
#include "mic.h"

/// @brief 主动推送配置
#define PUSH_METER_LASTGASP_ENABLE true    ///@配置是否使能主动推送停电上报数据


/// control 继电器控制模块配置
#define CONTROL_SUPPORT_ENABLE false                           ///@配置是否使能继电器控制功能
#define RELAY_CONTROL_ENABLE 1                                ///@使能继电器控制模块
#define RELAY_CONTROL_MIN_VOLTAGE (MIN_WORK_VOLTAGE / 10)     ///@继电器控制时最小的工作电压-指市电 单位V
#define RELAY_CONTROL_ONPERMIT_TO_ON_ENALBE 0                 ///@继电器运行合闸时直接合闸
#define RELAY_PWRON_STABLE_TIME 5                             ///@配置继电器控制上电稳定时间
#define RELAY_PWRON_DELAY_TIME 120                            ///@配置继电器控制上电延时时间
#define RELAY_CHECK_ENABLE (USE_RLY_CHK || USE_RLY_CHK_EX)    ///@配置是否使能继电器状态检测功能
#define RELAY_MALIGNANT_LOAD_EN false                         ///@配置是否使能恶性负载检测功能
#define RELAY_CURRENT_EVT_AUTO_RECOVERY true                  ///@配置是否使能电流类事件导致拉闸自动恢复功能，

/// 状态模块配置
#define EXTBATT_LOW_STA_TIME 60    ///@配置外部电池开始检测时长, 单位秒
#define EXTBATT_LOW_END_TIME 60    ///@配置外部电池结束检测时长, 单位秒
#define INTBATT_LOW_STA_TIME 60    ///@配置内部电池开始检测时长, 单位秒
#define INTBATT_LOW_END_TIME 60    ///@配置内部电池结束检测时长, 单位秒
#define MIC_FAULT_DET_TIME 60      ///@配置计量错误检测时长, 单位秒
#define EXTBATT_LOW_THD 5000       ///@配置外部电池电压低门限，单位0.001V
#define EXTBATT_NORMAL_THD 5500    ///@配置外部电池正常门限，单位0.001V
#define INTBATT_LOW_THD 3100       ///@配置内部电池电压低门限，单位0.001V
#define INTBATT_NORMAL_THD 3300    ///@配置内部电池正常门限，单位0.001V

//// 时钟相关配置参数
#define DAYLIGHT_SAVING_EN true
#define BROADCAST_CLOCK_PERIOD_LIMIT (60 * 10)    /// 0点前后10分钟，不接收广播命令**需求是广播校时不应该跨天和结算日。
#define BROADCAST_CLOCK_ADJ_LIMIT_MAX (5 * 60)    /// 5分钟以上的误差，不接收广播命令, 默认值，可设置。
#define BROADCAST_CLOCK_ADJ_LIMIT_MIN 60          /// 60 秒以内的误差，不接收广播命令，默认值，可设置。
#define BROADCAST_CLOCK_ADJ_NUM_LIMIT 1           /// 每天（自然日）只接受1次广播校时

#define NT_ADJ_LIMIT_MAX (3600 * 24 * 365)        /// 网络校时最大偏差，单位秒, 1 年, T4表生产必须校时
#define NT_ADJ_LIMIT_MIN (3)                      /// 网络校时最小偏差，单位秒,
#define NT_ADJ_YEAR_MIN 25                        /// 网络校时最小年份，单位年
#define NT_ADJ_YEAR_MAX 36                        /// 网络校时最大年份，单位年,超过2036年，网络时间不可靠的问题

/// ***电能模块配置******************************************************************
#if defined(POLYPHASE_METER)
#define ENERGY_PHASE_ENABLE true        /// 是否记录分相电能
#define ENERGY_PHASE_TARIFF_EN false    /// 是否记录分相费率电能
#define ENERGY_REACTIVE_ENABLE true     /// 是否记录无功电能
#else
#define ENERGY_PHASE_ENABLE false       /// 是否记录分相电能
#define ENERGY_PHASE_TARIFF_EN false    /// 是否记录分相费率电能
#define ENERGY_REACTIVE_ENABLE false    /// 是否记录无功电能
#endif

#define ENERGY_NEG_TO_POS_EN false      /// 是否支持能量反向正计
#define ENERGY_REA_LAG_LEAD_EN false    /// 是否使能无功按超前滞后计算
#define ENERGY_APP_ENABLE true          /// 是否使能视在电能计量
/// 电能量纲
#define ENERGY_DEF_SCALER 0.01    /// 整型格式电能量纲, 单位kwh
#define EN_SCALER_2 \
    1    /// 645 默认传输4位小数，可设置；显示默认2位小数，可设置可借位显示；冻结默认存储4位小数，可设置。
#define EN_SCALER_4 1    /// 传输4位小数，可设置；显示默认2位小数，可设置可借位显示；冻结默认存储4位小数，可设置。
typedef uint32_t ENERGY_DEF_FORMAT;

// 费率模块配置
// 年时区数(p≤14) unsigned，
// 日时段表数（q≤8） unsigned，
// 日时段数(每日切换数)（m≤14） unsigned，
// 费率数（k≤63） unsigned，
// 公共假日数（n≤254） unsigned
#define TARIFF_ZONE_NUM 14               /// 定义最大时区数
#define TARIFF_PERIOD_NUM 14             /// 定义最大时段数
#define TARIFF_SCH_TAB_NUM 8             /// 定义最大日时段表数
#define TARIFF_RATE_NUM 8                /// 定义最大费率数(rate从1开始)
#define TARIFF_HOLIDAY_NUM 50            /// 定义最大节假日数(特殊日)
#define TARIFF_HOLIDAY_ACTIVATE false    /// 定义是否支持节假日表激活passive

/// step_tariff 阶梯费率模块配置项******************************************************************
// 电能表阶梯电价功能要求如下：
// a） 本地费控电能表具有两套阶梯电价，并可在设置时间点启用另一套阶梯电价计费；支持以月、
// 年为计费周期的阶梯算费方式，称为月阶梯、年阶梯，并支持电能表在指定时间实现两种方式
// 自动切换；
// b） 月阶梯方式下，阶梯用电量在 DL/T 698.45 中的第 1 阶梯结算日进行转存，转存后当前阶梯用
// 电量清零；
// c） 年阶梯方式下，阶梯用电量在每个月、日、时有效的阶梯结算日均进行转存，转存后当前阶梯
// 用电量清零。阶梯结算日只能是 1 月至 12 月中某月的 1 日至 28 日内的整点时刻，设置为其它
// 数据则不执行年阶梯；
// d） 阶梯结算日只用于阶梯用电量结算，电能示值、需量还按月结算日转存。两套阶梯结算日的切
// 换时间采用备用套阶梯切换时间，和两套阶梯同时切换；
// e） 两套阶梯参数、阶梯切换时间适用于月阶梯、年阶梯，执行年阶梯时，则不再执行月阶梯。
#define STEP_TARIFF_NUM 7                                       ///@配置定义最大阶梯数, 最小必须为1，7个阶梯6个阶梯值
#define STEP_TARIFF_BILLING_NUM 4                               ///@配置年阶梯结算日最大数，4个阶梯计算日
#define STEP_TARIFF_ENERGY_SCALER (ENERGY_DEF_SCALER * 1000)    ///@配置阶梯电能单位量纲10

/// billing 结算模块配置项******************************************************************
#define BILLING_MONTH_LOG_NUM 12     ///@配置定义最大月结记录数
#define BILLING_MONTHLY_DAY_NUM 3    ///@配置定义最大月结算日数，默认3
#define BILLING_DAY_THD 28           ///@配置定义月结算日范围，1-28
#define BILLING_STEP_LOG_NUM 4       ///@配置定义阶梯结算记录数
#define BILLING_DAILY_LOG_NUM 31     ///@配置定义日结算记录数

/// loadcurve 负荷记录模块配置项******************************************************************
#define LC1_ENABLE true     ///@配置负荷曲线1自由曲线使能
#define LC1_PERIOD 60       //(15*60)    ///@配置负荷曲线1默认捕获周期，秒
#define LC1_DAYS 2          // 31    ///@配置负荷曲线1保存天数
#define LC2_ENABLE false    ///@配置负荷曲线2自由曲线使能
#define LC2_PERIOD 60       //(15*60)    ///@配置负荷曲线2默认捕获周期，秒
#define LC2_DAYS 2          // 10    ///@配置负荷曲线2保存天数
#define LC3_ENABLE false    ///@配置负荷曲线3自由曲线使能
#define LC4_ENABLE false    ///@配置负荷曲线4自由曲线使能
#define LC5_ENABLE false    ///@配置负荷曲线5自由曲线使能
#define LC6_ENABLE false    ///@配置负荷曲线6自由曲线使能

#if (LC1_PERIOD < 20 || LC2_PERIOD < 20)
#error “禁止设置过短周期！”
#endif

/// prepayment 预付费模块配置项******************************************************************
typedef int32_t PAY_CREDIT_DATA_TYPE;
#define PAYT_ACCOUNT_ENERGY TYPE_ENERGY_ADD_ACT    ///@配置结算电能  正向+反向有功组合电能
#define PAY_LOCAL_ENABLE true                      ///@配置是否使能本地费控功能，远程费控不用阶梯电价模块，和扣费模块
#define PAY_CURRENCY_IS_ENERGY false               ///@配置是否以电能计费  false:以金额计费 true:以电能计费
#define DEF_LOW_CRDT_LVL1 2000                     ///@配置默认低信用门限1  0.01
#define DEF_LOW_CRDT_LVL2 1000                     ///@配置默认低信用门限2  0.01

#if SW_PAYMENT_EN
#if PAY_CURRENCY_IS_ENERGY
#define PAY_MODE 01    ///@配置费控模式 01: 电能计费 02: 金额计费
#else
#define PAY_MODE 02
#endif
#else
#define PAY_MODE 00    ///@配置费控模式 00: 关闭 01: 电能计费 02: 金额计费
#endif

#if PAY_CURRENCY_IS_ENERGY
#define PAY_CURRENCY_SCALE -2       ///@配置货币量纲
#define PAY_CURRENCY_SCALE_2 100    ///@配置货币量纲 = 10^(-PAY_CURRENCY_SCALE) = 100
#define PAY_CREDIT_UNIT UNIT_WH
#define PAY_CREDIT_SCALE (PAY_CURRENCY_SCALE + 3)    ///@配置余额量纲
#define PAY_PRICE_SCALE_KWH -4                       ///@配置扣费电能量纲
#define PAY_PRICE_SCALE (PAY_PRICE_SCALE_KWH + 3)
#define PAY_RATIO \
    100    ///@配置扣费比例(货币单位量纲与价格单位量纲换算成比例值 = 10^(PAY_CURRENCY_SCALE - PAY_PRICE_SCALE_KWH) 次方)
           ///100 = 10^(-2-4)
#else
#define PAY_CURRENCY_SCALE -2       ///@配置货币量纲
#define PAY_CURRENCY_SCALE_2 100    ///@配置货币量纲 = 10^(-PAY_CURRENCY_SCALE) = 100
#define PAY_CREDIT_UNIT UNIT_CURRENCY
#define PAY_CREDIT_SCALE (PAY_CURRENCY_SCALE)    ///@配置余额量纲
#define PAY_PRICE_SCALE_KWH -4                   ///@配置扣费金额量纲
#define PAY_PRICE_SCALE (PAY_PRICE_SCALE_KWH)
#define PAY_RATIO \
    100    ///@配置扣费比例(货币单位量纲与价格单位量纲换算成比例值 = 10^(PAY_CURRENCY_SCALE - PAY_PRICE_SCALE_KWH) 次方)
           ///100 = 10^(-2-4)
#endif

/// display 显示模块配置项******************************************************************
#define DISP_AUTO_UNIT_EN false             ///@配置支持电能显示单位扩展(自动显示单位，Wh,kWh,MWh)
#define DISP_SIGN_STRENGTH_EN true          ///@配置信号强度显示
#define DISP_ICON_COMM_ENABLE true          ///@配置是否使能通讯图标显示
#define DISP_ENG_DIGT_AUTO_EN true          ///@配置是否使能电能借位显示(自动小数位数)
#define DISP_EN_UNIT_EXPEND_DISPLAY true    ///@配置支持电能显示单位扩展
#define DISP_OI true                        ///@配置是否使能数据标识显示
#define DISP_MANUAL_DISPLAY true            ///@配置支持按键显示
#define DISP_POWER_DOWN_DISPLAY (USE_BTN_DISP_UP && USE_BTN_DISP_DN && true)    ///@配置支持掉电后显示
#define DISP_DISP_AUTO_EVENT true                                               ///@配置自动轮显中是否实时插入报警显示
#define DISP_POWER_DOWN_FOREVER false                                           ///@配置掉电常显
#define DISP_POWER_DOWN_BTN_TIMEOUT \
    false    ///@配置掉电按键超时显示 true:显示一屏超时后后关闭LCD false:循环显示唤醒显示列表，显示到最后一屏后关闭LCD
#define DISP_MAX_VAL 9999999999                 ///@配置LCD所能支持的最大显示值
#define DISP_ID_MAX_NUM 64                      ///@配置每个显示列表允许显示数据项数量
#define DISP_PWRON_DISP_TIME 5                  ///@配置开机后默认全显时间，单位秒
#define DISP_PWDN_OUT_TIME 10                   ///@配置掉电后开盖显示超时时间
#define DISP_KEYIN_MODE_OUTTIME 10              ///@配置输入代码超时时间
#define DISP_TOKEN_OUTTIME 60                   ///@配置输入代码超时无效时间
#define DISP_MSG_MAX_LEN (6 * LCD_MS_DIGITS)    ///@配置消息显示最大长度，最大支持6屏


/// demand 需量模块配置项******************************************************************
#define DEMAND_DEF_PERIOD \
    (2)    ///@配置定义默认需量周期, 单位m 需量周期应为滑差时间的 5 的整倍数。出厂默认值：需量周期 15min、滑差时间 1min
#define DEMAND_DEF_SLIP_PERIOD \
    (1)    ///@配置定义默认滑差周期, 单位m 需量周期应为滑差时间的 5 的整倍数。出厂默认值：需量周期 15min、滑差时间 1min
#define DEMAND_MAX_SLIP_NUM 60      ///@配置定义最大滑差周期数(需量周期与滑差时间的倍数) 滑差1分钟，周期60分钟
#define DEMAND_PARA_UNIFIED true    ///@配置定义需量参数是否针对所有对象
#define DEMAND_TARIFF_RATE_NUM 0    ///@配置定义费率需量TARIFF_RATE_NUM, 0表示无费率需量

#define DEMAND_ADD_ACT_ENABLE true     ///@配置是否支持总有功需量 (始终支持)
#define DEMAND_ADD_REA_ENABLE false    ///@配置是否支持总无功需量
#define DEMAND_ADD_APP_ENABLE false    ///@配置是否支持总视在需量
#define DEMAND_SUB_ACT_ENABLE false    ///@配置是否支持正向-反向有功需量
#define DEMAND_POS_ACT_ENABLE true     ///@配置是否支持正向有功需量
#define DEMAND_NEG_ACT_ENABLE true     ///@配置是否支持反向有功需量
#define DEMAND_POS_REA_ENABLE false    ///@配置是否支持正向无功需量
#define DEMAND_NEG_REA_ENABLE false    ///@配置是否支持反向无功需量
#define DEMAND_Qx_REA_ENABLE false     ///@配置是否支持四象限无功需量
#define DEMAND_POS_APP_ENABLE false    ///@配置是否支持正向视在需量
#define DEMAND_NEG_APP_ENABLE false    ///@配置是否支持反向视在需量
#define DEMAND_CUM_SUM_ENABLE false    ///@配置是否支持累计最大需量

/// event 事件模块配置项******************************************************************
#define EVENT_MODULE_ENABLE true    ///@配置是否使能事件模块
#if defined(POLYPHASE_METER)
#define EVENT_POWER_EN true    ///@配置是否使能欠压类事件
#else
#define EVENT_POWER_EN false    ///@配置是否使能欠压类事件
#endif
#define EVENT_LOSS_VOL_EN (EVENT_POWER_EN && true)    ///@配置是否使能失压事件
#define EVENT_LOW_VOL_EN (EVENT_POWER_EN && true)     ///@配置是否使能欠压事件
#define EVENT_OVR_VOL_EN (EVENT_POWER_EN && true)     ///@配置是否使能过压事件
#define EVENT_MISS_VOL_EN (EVENT_POWER_EN && true)    ///@配置是否使能断相事件
#define EVENT_ALL_LOSS_VOL_EN \
    (EVENT_POWER_EN && false)    ///@配置是否使能全失压事件.注意开启硬件层 USE_EMU_AT_LOSS_VOLTAGE
#define EVENT_BAK_PWR_LOS_EN (EVENT_POWER_EN && false)    ///@配置是否使能辅助电源失电事件
#define EVENT_V_REV_SQR_EN (EVENT_POWER_EN && false)      ///@配置是否使能电压逆向序事件
#define EVENT_I_REV_SQR_EN (EVENT_POWER_EN && false)      ///@配置是否使能电流逆向序事件
#define EVENT_V_UNB_EN (EVENT_POWER_EN && false)          ///@配置是否使能电压不平衡事件
#define EVENT_I_UNB_EN (EVENT_POWER_EN && false)          ///@配置是否使能电流不平衡事件
#define EVENT_LOS_CUR_EN (EVENT_POWER_EN && false)        ///@配置是否使能失流事件
#define EVENT_OVR_CUR_EN (EVENT_POWER_EN && false)        ///@配置是否使能过流事件
#define EVENT_MISS_CUR_EN (EVENT_POWER_EN && false)       ///@配置是否使能断流事件
#define EVENT_REV_EN (EVENT_POWER_EN && false)            ///@配置是否使能潮流反向事件
#define EVENT_OVR_LOAD_EN (EVENT_POWER_EN && false)       ///@配置是否使能过载事件

#define EVENT_LOW_PF_EN false             ///@配置是否使能低功率因素事件
#define EVENT_CONTROL_EN true             ///@配置是否使能拉合闸记录
#define EVENT_PWR_DOWN_EN true            ///@配置是否使能掉电事件
#define EVENT_OVR_DM_EN false             ///@配置是否使能超需量事件
#define EVENT_PROGRAM_EN false            ///@配置是否使能编程事件
#define EVENT_METER_CLEAN_EN true         ///@配置是否使能电表清零事件
#define EVENT_DEMAND_CLEAN_EN true        ///@配置是否使能需量清零事件
#define EVENT_EVENT_CLEAN_EN true         ///@配置是否使能事件清零事件
#define EVENT_SHITFT_TIME_EN true         ///@配置是否使能校时事件
#define EVENT_BC_TIME_EN true             ///@配置是否使能广播校时事件
#define EVENT_SCHEDULE_EN false           ///@配置是否使能时段表事件
#define EVENT_ZONE_TAB_EN false           ///@配置是否使能时区表事件
#define EVENT_WEEKENDS_PGM_EN false       ///@配置是否使能周休日编程事件
#define EVENT_HOLIDAY_PGM_EN false        ///@配置是否使能节假日表编程事件
#define EVENT_COMB_kWh_PGM_EN false       ///@配置是否使能有功组合方式编程事件
#define EVENT_COMB1_kvarh_PGM_EN false    ///@配置是否使能无功组合方式1编程事件
#define EVENT_COMB2_kvarh_PGM_EN false    ///@配置是否使能无功组合方式2编程事件
#define EVENT_BL_DAY_PGM_EN false         ///@配置是否使能结算日编程事件
#define EVENT_METER_COVER_EN true         ///@配置是否使能开表盖事件
#define EVENT_TEM_COVER_EN true           ///@配置是否使能开端盖事件

/// power_event 电源事件模块配置项******************************************************************
#define PQ_PWON_TIME_THD 2    ///@配置上电检测时长，单位秒
#define PQ_PWDN_TIME_THD 2    ///@配置掉电检测时长，单位秒

// 模块类型定义----------------------------
/// @brief 需量模块类型定义
typedef enum
{
    TYPE_DEMAND_ADD_ACT,
#if DEMAND_SUB_ACT_ENABLE
    TYPE_DEMAND_SUB_ACT,
#endif
#if DEMAND_ADD_REA_ENABLE
    TYPE_DEMAND_ADD_REA,
#endif
#if DEMAND_ADD_APP_ENABLE
    TYPE_DEMAND_ADD_APP,
#endif
#if DEMAND_POS_ACT_ENABLE
    TYPE_DEMAND_POS_ACT,
#endif
#if DEMAND_NEG_ACT_ENABLE
    TYPE_DEMAND_NEG_ACT,
#endif
#if DEMAND_POS_REA_ENABLE
    TYPE_DEMAND_POS_REA,
#endif
#if DEMAND_NEG_REA_ENABLE
    TYPE_DEMAND_NEG_REA,
#endif
#if DEMAND_Qx_REA_ENABLE
    TYPE_DEMAND_Q1_REA,
    TYPE_DEMAND_Q2_REA,
    TYPE_DEMAND_Q3_REA,
    TYPE_DEMAND_Q4_REA,
#endif
#if DEMAND_POS_APP_ENABLE
    TYPE_DEMAND_POS_APP,
#endif
#if DEMAND_NEG_APP_ENABLE
    TYPE_DEMAND_NEG_APP,
#endif

    DEMAND_TYPE_NUM
} demand_type_t;    // 与 dd_energy_type 对应

typedef enum
{
    TYPE_BL_COM_ACT,       // 组合有功
    TYPE_BL_EN_ADD_ACT,    // 正向有功
    TYPE_BL_EN_SUB_ACT,    // 反向有功
    TYPE_BL_COM1_REA,      // 组合无功1
    TYPE_BL_COM2_REA,      // 组合无功2
    TYPE_BL_EN_Q1_REA,
    TYPE_BL_EN_Q2_REA,
    TYPE_BL_EN_Q3_REA,
    TYPE_BL_EN_Q4_REA,
    BL_EN_TYPE_NUM
} BL_EN_TYPE_t;

#endif    // __APP_CONFIG_H__
