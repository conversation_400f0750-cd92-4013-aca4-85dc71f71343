#ifndef _BCD_H
#define _BCD_H

#include <stddef.h>
#include <stdint.h>
#include <stdbool.h>
#include <string.h>
#include <stdlib.h>
#include <math.h>
#include "crc.h"

/// 获取最大值
#ifndef max
    #define max(x, y)    (((x) > (y)) ? (x) : (y))
#endif

/// 获取最小值
#ifndef min
    #define min(x, y)    (((x) < (y)) ? (x) : (y))
#endif

/// 浮点数据比较
#define eps (1e-6)
/// 浮点型a等于b
#define equ(a,b)        ((fabs((a)-(b))) < (eps))
/// 浮点型a大于b
#define more(a,b)       (((a) - (b)) > (eps))
//// 浮点型a小于b
#define less(a,b)       (((b) - (a)) > (eps))
/// 浮点型a大于等于b
#define more_equ(a,b)   (((a) - (b)) > (-eps))
/// 浮点型a小于等于b
#define less_equ(a,b)   (((b) - (a)) > (-eps))

/// d/n，四舍五入
#ifndef cdiv
    #define cdiv(d, n)    (((d) + (n) / 2) / (n))
#endif

/// 16进制判断
#define ishexdigit(c) ((c >= '0' && c <= '9') || (c >= 'A' && c <= 'F') || (c >= 'a' && c <= 'f'))

/// BCD转10进制hex
#define bcdtob(x)   (uint8_t)(((x) >> 4) * 10 + ((x) & 0x0F))

/// BCD字符转16进制
#define btobcd(x)   (uint8_t)((((x)/10)<<4) + ((x)%10))



extern uint16_t get_lsbdata16(const uint8_t* p);
extern void     set_msbdata16(uint8_t* p, uint16_t dat);
extern void     set_lsbdata16(uint8_t* p, uint16_t dat);
extern uint16_t get_msbdata16(const uint8_t* p);
extern uint32_t get_lsbdata32(const uint8_t* p);
extern void     set_lsbdata32(uint8_t* p, uint32_t dat);
extern void     set_msbdata32(uint8_t* p, uint32_t dat);
extern uint32_t get_msbdata32(const uint8_t* p);
extern float    get_lsbfloat32(const uint8_t* p);
extern void     set_lsbfloat32(uint8_t* p, float dat);
extern float    get_msbfloat32(const uint8_t* p);
extern void     set_msbfloat32(uint8_t* p, float dat);
extern uint8_t  cal_checksum8(const void* dat, uint16_t size);
extern void     bcd_to_lsbhex(uint8_t *buf, const uint8_t* bcd, uint16_t len);
extern void     bcd_to_msbhex(uint8_t *buf, const uint8_t* bcd, uint16_t len);
extern void     hex_to_msbbcd(uint8_t *bcd, const uint8_t* buff, uint16_t len);
extern void     hex_to_lsbbcd(uint8_t *bcd, const uint8_t* buff, uint16_t len);
extern void     uint32_to_msbbcd(uint8_t* bcd, uint32_t hex32, uint8_t num);
extern void     uint32_to_lsbbcd(uint8_t* bcd, uint32_t hex32, uint8_t num);
extern void     int32_to_lsbbcd(uint8_t* bcd, int32_t hex32, uint8_t num);
extern uint32_t lsbbcd_to_hex32(const uint8_t* bcd, uint8_t num);
extern uint32_t msbbcd_to_hex32(const uint8_t* bcd, uint8_t num);
extern double   pow10f(int8_t x);
extern uint16_t hexstr_to_charstr(char* str, const uint8_t* hex, uint32_t size);
extern uint16_t string_to_hexarray(uint8_t *hex, const char *str, uint16_t size);
extern void     string_reverse(void* str, uint32_t size);
extern void     strcpy_reverse(void *des, const void *src, uint32_t size);
extern uint8_t  byte_reverse(uint8_t c);
extern uint32_t json_get_value(const char *json, const char *key, char *value, bool need_hex);
extern uint32_t calculate_timestamp(uint8_t *dt);

#endif
