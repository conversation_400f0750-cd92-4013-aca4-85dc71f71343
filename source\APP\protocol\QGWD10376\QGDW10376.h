/**
 ******************************************************************************
 * @file    QGWD10376.h
 * <AUTHOR> @date    2025
 * @brief
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2025  SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/
#ifndef __QGWD10376_H
#define __QGWD10376_H

#include "typedef.h"

typedef enum function_code_enum
{
    FUN_VERIFY            = 0x00,    ///< 验证
    FUN_RESET             = 0x01,    ///< 复位
    FUN_LINK_TEST         = 0x02,    ///< 链路测试
    FUN_RELAY_CMD         = 0x03,    ///< 中继站命令
    FUN_SET_PARAM         = 0x04,    ///< 设置参数
    FUN_CTRL_CMD          = 0x05,    ///< 控制命令q
    FUN_AUTH_KEY          = 0x06,    ///< 身份认证及密钥协商
    FUN_RESERVED          = 0x07,    ///< 备用
    FUN_REQ_ACTIVE_REPORT = 0x08,    ///< 请求被级联终端主动上报
    FUN_REQ_TERMINAL_CFG  = 0x09,    ///< 请求终端配置
    FUN_QUERY_PARAM       = 0x0A,    ///< 查询参数
    FUN_REQ_TASK_DATA     = 0x0B,    ///< 请求任务数据
    FUN_REQ_CLASS1_DATA   = 0x0C,    ///< 请求 1 类数据（实时数据）
    FUN_REQ_CLASS2_DATA   = 0x0D,    ///< 请求 2 类数据（历史数据）
    FUN_REQ_CLASS3_DATA   = 0x0E,    ///< 请求 3 类数据（事件数据）
    FUN_FILE_TRANSFER     = 0x0F,    ///< 文件传输
    FUN_DATA_FORWARD      = 0x10,    ///< 数据转发
    FUN_RESERVED_11
} fun_code_t;

typedef struct gdw376_frame_struct
{
    uint8_t head;     ///< 帧头
    uint8_t lenl;     ///< 帧长度低字节
    uint8_t lenh;     ///< 帧长度高字节
    uint8_t lenl2;    ///< 帧长度低字节2
    uint8_t lenh2;    ///< 帧长度高字节2
    uint8_t head2;    ///< 帧头2
    ///< 控制域C
    struct
    {
        uint8_t fun : 4;
        uint8_t fcv : 1;
        uint8_t fcb_acd : 1;
        uint8_t prm : 1;
        uint8_t dir : 1;
    } c;
    uint8_t  a1[2];    ///< 地址域A1
    uint8_t  a2[2];    ///< 地址域A2
    uint8_t  a3;       ///< 地址域A3
    uint8_t  afn;      ///< 应用层功能码AFN
    uint8_t  seq;      ///< 帧序列域SEQ
    uint8_t *dat;      ///< 数据域DAT
} gdw376_frame_s;

struct gdw376_s
{
    /// @brief 初始化
    void (*init)(uint8_t chn, uint8_t *buff);
    /// @brief 协议处理
    uint16_t (*msg_process)(uint8_t chn, uint8_t *ack, uint16_t len);
    /// @brief 获取心跳帧
    uint16_t (*get_heartbeat_frame)(uint8_t *buf);
    /// @brief 心跳帧ack确认
    bool (*heartbeat_ack)(const uint8_t *buf, uint16_t len);
    /// @brief 获取登录帧
    uint16_t (*get_login_frame)(uint8_t *buf);
    /// @brief 登录ack确认
    bool (*login_ack)(const uint8_t *buf, uint16_t len);
    /// @brief 获取停电上报数据
    uint16_t (*get_lastgasp_frame)(uint8_t *buf);
};

extern const struct gdw376_s gdw376;

#endif    // __QGWD10376_H
//
