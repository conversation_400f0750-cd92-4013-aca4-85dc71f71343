/**
 *@description:
 *@return {*}
 **/
#include "module_para.h"
#include "datastore.h"
#include "string.h"
#include "utils.h"
#include "crc.h"

// 定义模块参数地址索引，本文件私有
#define MODULE_PARA_ADDR (nvm_addr(NVM_MODULE_PARA))

#define MODULE_PARA_CRC16 0    /// 只能在测试阶段修改，产品发布后不得再修改！！！！否则会丢失数据
#define CRC16_CHK(struct, len) STRUCT_CRC16_CHK(MODULE_PARA_CRC16, struct, len)
#define CRC16_CAL(struct, len) STRUCT_CRC16_GET(MODULE_PARA_CRC16, struct, len)

static const module_para_s *module_running_para;    /// 模块运行参数

const module_para_s module_default_para = {
    .tcp            = {11, "***********", 8090},    // 默认主站IP地址
    .tcp_bak        = {11, "***********", 8090},
    .ntp_server     = {15, "ntp.aliyun.com", 123},    // 默认NTP服务器地址
    .ntp_server_bak = {15, "time.windows.com", 0},
    .http_url       = {97, "http://hardware-package.oss-cn-beijing.aliyuncs.com/1/DDDD1980(5-60A)-T4-app(V0.30-20250704).bin"},    // 默认HTTP下载地址
};

/// @brief 模块参数保存函数
static bool module_para_save(uint16_t ofst, const void *val, uint16_t len)
{
    module_para_s para;
    if(ofst != 0) memcpy(&para, module_running_para, sizeof(module_para_s));
    memcpy((uint8_t *)&para + ofst, val, len);
    CRC16_CAL(&para, sizeof(module_para_s));
    module_running_para = (const module_para_s *)MODULE_PARA_ADDR;
    return nvm.write((uint32_t)module_running_para, &para, sizeof(module_para_s));
}

/// @brief 模块参数指针初始化并检验参数
static void module_para_load(void)
{
    module_running_para = (const module_para_s *)MODULE_PARA_ADDR;
    if(CRC16_CHK(module_running_para, sizeof(module_para_s)) == false)
    {
        module_running_para = &module_default_para;    // 如果CRC校验失败，使用默认参数
    }
}

void module_init(void)
{
    module_para_load();    // 初始化模块参数
}

void module_reset(uint8_t type)
{
    if(type & SYS_PARA_RESET)
    {
        /* 恢复默认参数 */
        module_para_save(0, &module_default_para, sizeof(module_para_s));
    }
    if(type & SYS_DATA_RESET) { /* 恢复默认数据 */ }
}

/// @brief 获取模块参数
/// @param buf 缓冲区指针
/// @param typ 模块参数类型
/// @return 返回数据长度，当为端口号时，返回uint16_t类型的端口号
uint16_t module_para_get(void *buf, MODULE_PARA_t typ)
{
    const module_para_s *para = module_running_para;
    uint8_t             *pbuf = (uint8_t *)buf;
    uint16_t             len  = 0;

    if(pbuf == NULL || typ > MODULE_HTTP_URL) return 0;    // 参数错误

    switch(typ)
    {
        case MODULE_TCP:
            len = para->tcp.len;                                    // 获取IP地址长度
            if(len == 0 || len > sizeof(para->tcp.ip)) return 0;    // IP地址长度为0或超过最大长度
            memcpy(pbuf, para->tcp.ip, len);
            break;
        case MODULE_TCP_PORT:
            return para->tcp.port;    // 返回主IP端口号

        case MODULE_TCP_BAK:
            len = para->tcp_bak.len;                                    // 获取备用IP地址长度
            if(len == 0 || len > sizeof(para->tcp_bak.ip)) return 0;    // 备用IP地址长度为0或超过最大长度
            memcpy(pbuf, para->tcp_bak.ip, len);
            break;
        case MODULE_TCP_BAK_PORT:
            return para->tcp_bak.port;    // 返回备用IP端口号

        case MODULE_NTP_SERVER:
            len = para->ntp_server.len;                                    // 获取NTP服务器地址长度
            if(len == 0 || len > sizeof(para->ntp_server.ip)) return 0;    // NTP服务器地址长度为0或超过最大长度
            memcpy(pbuf, para->ntp_server.ip, len);
            break;
        case MODULE_NTP_SERVER_BAK:
            len = para->ntp_server_bak.len;                                    // 获取备用NTP服务器地址长度
            if(len == 0 || len > sizeof(para->ntp_server_bak.ip)) return 0;    // 备用NTP服务器地址长度为0或超过最大长度
            memcpy(pbuf, para->ntp_server_bak.ip, len);
            break;
        case MODULE_HTTP_URL:
            len = para->http_url.len;                                     // 获取HTTP下载地址长度
            if(len == 0 || len > sizeof(para->http_url.url)) return 0;    // HTTP下载地址长度为0或超过最大长度
            memcpy(pbuf, &para->http_url.url, len);                       // 复制长度到缓冲区
            break;
        default:
            break;
    }
    return len;
}

/// @brief 设置模块参数
/// @param buf 缓冲区指针
/// @param len 长度
/// @param typ 模块参数类型
/// @return 返回true表示设置成功，false表示设置失败
bool module_para_set(uint8_t *buf, uint16_t len, MODULE_PARA_t typ)
{
    uint16_t dlen;
    uint16_t ofst;

    if(buf == NULL || typ > MODULE_HTTP_URL || len == 0) return false;    // 参数错误

    switch(typ)
    {
        case MODULE_TCP:
            dlen = member_size(module_para_s, tcp);
            ofst = member_offset(module_para_s, tcp);
            break;
        case MODULE_TCP_PORT:
            dlen = member_size(module_para_s, tcp.port);
            ofst = member_offset(module_para_s, tcp.port);
            break;
        case MODULE_TCP_BAK:
            dlen = member_size(module_para_s, tcp_bak);
            ofst = member_offset(module_para_s, tcp_bak);
            break;
        case MODULE_TCP_BAK_PORT:
            dlen = member_size(module_para_s, tcp_bak.port);
            ofst = member_offset(module_para_s, tcp_bak.port);
            break;
        case MODULE_NTP_SERVER:
            dlen = member_size(module_para_s, ntp_server);
            ofst = member_offset(module_para_s, ntp_server);
            break;
        case MODULE_NTP_SERVER_BAK:
            dlen = member_size(module_para_s, ntp_server_bak);
            ofst = member_offset(module_para_s, ntp_server_bak);
            break;
        case MODULE_HTTP_URL:
            dlen = member_size(module_para_s, http_url);
            ofst = member_offset(module_para_s, http_url);
            break;
        default:
            return false;    // 不支持的参数类型
    }
    if(len > dlen) { return false; }            // 如果长度超过最大长度
    return module_para_save(ofst, buf, len);    // 保存参数
}

const struct module_data_api_s module_para = {
    .init     = module_init,
    .reset    = module_reset,
    .para_get = module_para_get,
    .para_set = module_para_set,
};
