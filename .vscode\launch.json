{
    // 使用 IntelliSense 了解相关属性。 
    // 悬停以查看现有属性的描述。
    // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "cspy",
            "request": "launch",
            "name": "Debug the active IAR Project with C-SPY",
            "workbenchPath": "${command:iar-config.toolchain}",
            "projectPath": "${command:iar-config.project-file}",
            "projectConfiguration": "${command:iar-config.project-configuration}",
            "buildBeforeDebugging": "AskOnFailure"
        },
        {
            "type": "cspy",
            "request": "launch",
            "name": "DTZY1980-t4-5_60a-software",
            "target": "arm",
            "program": "${workspaceFolder}\\project\\DTZY1980-ht6025\\Debug\\Exe\\app.out",
            "driver": "J-Link/J-Trace",
            "stopOnSymbol": true,
            "workbenchPath": "${command:iar-config.toolchain}",
            "projectPath": "${workspaceFolder}\\project\\DTZY1980-ht6025\\app.ewp",
            "projectConfiguration": "Debug",
            "driverOptions": [
                "--crun=disabled",
                "--endian=little",
                "--cpu=Cortex-M0",
                "--drv_reset_to_cpu_start",
                "--drv_vector_table_base=0x4400",
                "--fpu=None",
                "-p",
                "$TOOLKIT_DIR$\\CONFIG\\debugger\\Hitrendtech\\HT6X2X.SVD",
                "--semihosting",
                "--device=HT6X2X",
                "--drv_communication=USB0",
                "--drv_interface_speed=auto",
                "--jlink_initial_speed=1000",
                "--drv_default_breakpoint=1",
                "--jlink_reset_strategy=0,0",
                "--drv_interface=SWD",
                "--drv_catch_exceptions=0x000"
            ],
            "setupMacros": [
                "${cwd}/project/DTZY1980-ht6025/app.mac"
            ],
            "download": {
                "flashLoader": "$TOOLKIT_DIR$\\config\\flashloader\\Hitrendtech\\HT6X2X.board",
                "deviceMacros": []
            },
            "preLaunchTask": "DTZY1980-t4-5_60a-software-debug"
        },
        {
            "type": "cspy",
            "request": "launch",
            "name": "DDSU1980-t4-5_60a-software",
            "target": "arm",
            "program": "${workspaceFolder}\\project\\DDSU1980-ht6025\\Debug\\Exe\\app.out",
            "driver": "J-Link/J-Trace",
            "stopOnSymbol": true,
            "workbenchPath": "${command:iar-config.toolchain}",
            "projectPath": "${workspaceFolder}\\project\\DDSU1980-ht6025\\app.ewp",
            "projectConfiguration": "Debug",
            "driverOptions": [
                "--crun=disabled",
                "--endian=little",
                "--cpu=Cortex-M0",
                "--drv_reset_to_cpu_start",
                "--drv_vector_table_base=0x4400",
                "--fpu=None",
                "-p",
                "$TOOLKIT_DIR$\\CONFIG\\debugger\\Hitrendtech\\HT6X2X.SVD",
                "--semihosting",
                "--device=HT6X2X",
                "--drv_communication=USB0",
                "--drv_interface_speed=auto",
                "--jlink_initial_speed=1000",
                "--drv_default_breakpoint=1",
                "--jlink_reset_strategy=0,0",
                "--drv_interface=SWD",
                "--drv_catch_exceptions=0x000"
            ],
            "setupMacros": [
                "${cwd}/project/DDSU1980-ht6025/app.mac"
            ],
            "download": {
                "flashLoader": "$TOOLKIT_DIR$\\config\\flashloader\\Hitrendtech\\HT6X2X.board",
                "deviceMacros": []
            },
            "preLaunchTask": "DDSU1980-t4-5_60a-software-debug"
        }
    ]
}