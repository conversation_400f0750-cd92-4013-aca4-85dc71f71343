/**
 ******************************************************************************
 * @file    hal_gpio.h
 * <AUTHOR>
 * @version V1.0
 * @date    2024-08-05
 * @brief   This file contains all the functions prototypes for the GPIO
 * @note
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2016 SheWei Electrics Co.,Ltd. All rights reserved..
 *
 ******************************************************************************/
// #ifdef __cplusplus
//  extern "C" {
// #endif

#ifndef __HAL_GPIO_H
#define __HAL_GPIO_H

/* Includes ------------------------------------------------------------------*/
#include "hal_def.h"

// #define M0P_GPIO                               ((M0P_GPIO_TypeDef *)0x40020C00UL)
#define HC_GPIOA ((M0P_GPIO_TypeDef *)(0x40020C00UL + GpioPortA))
#define HC_GPIOB ((M0P_GPIO_TypeDef *)(0x40020C00UL + GpioPortB))
#define HC_GPIOC ((M0P_GPIO_TypeDef *)(0x40020C00UL + GpioPortC))
#define HC_GPIOD ((M0P_GPIO_TypeDef *)(0x40020C00UL + GpioPortD))
#define HC_GPIOE ((M0P_GPIO_TypeDef *)(0x40020C00UL + GpioPortE))
#define HC_GPIOF ((M0P_GPIO_TypeDef *)(0x40020C00UL + GpioPortF))

/*
 * @brief 系统引脚功能定义:
   -o  输出
   -i  输入
   -ii 中断输入
   -io 双向IO
   -fa 摸拟输入
   -fi 外设功能输入
   -fo 外设功能输出
   -ff 外设功能双向输入/出
   -fw PWM输出
*/
#define PIN_M_NTC2 HC_GPIOA, 0        // -fa NTC2
#define PIN_M_NTC1 HC_GPIOA, 1        // -fa NTC1
#define PIN_UART1_TXD HC_GPIOA, 2     // -fo 1路RS485通讯模块串口输出
#define PIN_UART1_RXD HC_GPIOA, 3     // -fi 1路RS485通讯模块串口输入
#define PIN_FLASH_CS HC_GPIOA, 4      // -o  FLASH CS
#define PIN_FLASH_SCLK HC_GPIOA, 5    // -o  FLASH SCLK
#define PIN_FLASH_MISO HC_GPIOA, 6    // -i  FLASH MISO
#define PIN_FLASH_MOSI HC_GPIOA, 7    // -o  FLASH MOSI
#define PIN_MCU_DO0 HC_GPIOA, 8       // -fo MCU DO0
#define PIN_UART0_TXD HC_GPIOA, 9     // -o  4G TXD
#define PIN_UART0_RXD HC_GPIOA, 10    // -i  4G RXD
#define PIN_EE_SCL HC_GPIOA, 11       // -o  EEPROM I2C 时钟线
#define PIN_EE_SDA HC_GPIOA, 12       // -io EEPROM I2C 数据线
#define PIN_SWIO HC_GPIOA, 13         // -io SWDIO
#define PIN_SWCLK HC_GPIOA, 14        // -io SWCLK
#define PIN_4G_POWER HC_GPIOA, 15     // -o  4G 电源使能

#define PIN_BATT_IN HC_GPIOB, 0      // -fa 内置3.6V电池测压
#define PIN_TEMP_NCP HC_GPIOB, 1     // -fa 测温
#define PIN_M_IO3 HC_GPIOB, 2        // -fi 开关量输入3
#define PIN_4G_PWRKEY HC_GPIOB, 3    // -0  4G 开关机控制
#define PIN_KEY1 HC_GPIOB, 4         // -i  按键1检测
#define PIN_KEY2 HC_GPIOB, 5         // -i  按键2检测
#define PIN_M_IO2 HC_GPIOB, 6        // -fi 开关量输入2
#define PIN_M_IO1 HC_GPIOB, 7        // -fi 开关量输入1
#define PIN_LCD_SCL HC_GPIOB, 8      // -o  LCD I2C 时钟线
#define PIN_LCD_SDA HC_GPIOB, 9      // -io LCD I2C 数据线
#define PIN_M_IO4 HC_GPIOB, 10       // -fi 开关量输入4
#define PIN_VIO_CTL HC_GPIOB, 11     // -o  3.3V IO控制
#define PIN_HT_DOUT HC_GPIOB, 12     // -o  计量芯片MCU数据输出
#define PIN_HT_DIN HC_GPIOB, 13      // -i  计量芯片MCU数据输入
#define PIN_HT_CLK HC_GPIOB, 14      // -o  计量芯片时钟输出
#define PIN_HT_CS HC_GPIOB, 15       // -o  计量芯片片选
#define PIN_UART2_RXD HC_GPIOC, 2    // uart2 RXD
#define PIN_UART2_TXD HC_GPIOC, 3    // uart2 TXD
#define PIN_CF1_IN HC_GPIOC, 4       // -ii CF1脉冲输入
#define PIN_CF2_IN HC_GPIOC, 5       // -ii CF2脉冲输入
#define PIN_LN_PW HC_GPIOC, 6        // -i  掉电检测
#define PIN_LCD_BG HC_GPIOC, 7       // -o  LCD背光灯
#define PIN_VLCD_CTRL HC_GPIOC, 8    // -o  LCD电源控制
#define PIN_MCU_DO1 HC_GPIOC, 9      // -fo MCU DO1
#define PIN_LVDIN0 HC_GPIOC, 13      // -i  LVD功能
#define PIN_32_OUT HC_GPIOC, 14      // -o  32.768 OUT
#define PIN_32_IN HC_GPIOC, 15       // -i  32.768 IN

#define FLASH_PW_PIN HC_GPIOC, 10     // -o  FLASH 电源使能
#define PIN_JL_EN HC_GPIOC, 11        // -o  +3.3V JL-EN
#define PIN_4G_WAKEUP HC_GPIOC, 12    // -o  4G 唤醒

#define PIN_4G_RST HC_GPIOD, 2    // -o  4G 复位

#define PIN_KEY3 HC_GPIOF, 0      // -i  按键3检测
#define PIN_KEY4 HC_GPIOF, 1      // -i  按键4检测
#define PIN_ER_CTL HC_GPIOF, 4    // -o  错误灯控制
#define PIN_F_05 HC_GPIOF, 5      // -
#define PIN_F_06 HC_GPIOF, 6      // -
#define PIN_F_07 HC_GPIOF, 7      // -

/* Exported macro ------------------------------------------------------------*/
/// 以下根据芯片手册寄存器描述进行配置。当没有相应功能时，应定义为空，不能直接注释掉
/* @brief 寄存器操作方式配置GPIO输入 */

#define HAL_GPIO_DIR_IN(port, pin) ((port->PADIR) |= (1 << (pin)))
/* @brief 寄存器操作方式配置GPIO输出 */
#define HAL_GPIO_DIR_OUT(port, pin) ((port->PADIR) &= ~(1 << (pin)))

/* @brief 寄存器操作方式配置获取GPIO输入电平 */
#define HAL_GPIO_IN_GET(port, pin) ((port->PAIN) & (1 << pin))
/* @brief 寄存器操作方式配置获取GPIO输出电平 */
#define HAL_GPIO_OUT_GET(port, pin) ((port->PAOUT) & (1 << pin))

/* @brief 寄存器操作方式配置GPIO输出低电平 */
#define HAL_GPIO_OUT_RST(port, pin) ((port->PABCLR) = (1 << pin))
/* @brief 寄存器操作方式配置GPIO输出高电平 */
#define HAL_GPIO_OUT_SET(port, pin) ((port->PABSET) = (1 << pin))

/* @brief 寄存器操作方式配置GPIO输出翻转电平 */
#define HAL_GPIO_OUT_REV(port, pin) (HAL_GPIO_OUT_GET(port, pin) ? HAL_GPIO_OUT_RST(port, pin) : HAL_GPIO_OUT_SET(port, pin))
/* @brief 寄存器操作方式配置GPIO上拉使能 */
#define HAL_GPIO_PTUP_EN(port, pin) ((port->PAPU) |= (1 << pin))
/* @brief 寄存器操作方式配置GPIO上拉关闭 */
#define HAL_GPIO_PTUP_DIS(port, pin) ((port->PAPU) &= ~(1 << pin))

/* @brief 寄存器操作方式配置GPIO下拉使能 */
#define HAL_GPIO_PTPD_EN(port, pin) ((port->PAPU) |= (1 << pin))
/* @brief 寄存器操作方式配置GPIO下拉拉关闭 */
#define HAL_GPIO_PTPD_DIS(port, pin) ((port->PAPU) &= ~(1 << pin))

/* @brief 寄存器操作方式配置GPIO开漏关闭 */
#define HAL_GPIO_PTOD_DIS(port, pin) ((port->PAOD) &= ~(1 << pin))
/* @brief 寄存器操作方式配置GPIO开漏开启 */
#define HAL_GPIO_PTOD_EN(port, pin) ((port->PAOD) |= (1 << pin))

/* @brief 寄存器操作方式配置GPIO关闭高驱动能力 */
#define HAL_GPIO_PTDR_DIS(port, pin) ((port->PAOD) |= (1 << pin))
/* @brief 寄存器操作方式配置GPIO开启高驱动能力 */
#define HAL_GPIO_PTDR_EN(port, pin) ((port->PAOD) &= | (1 << pin))

/* @brief 寄存器操作方式配置GPIO为模拟端口 */
#define HAL_GPIO_AIN(port, pin) ((port->PAADS) |= (1UL << (pin)))
/* @brief 寄存器操作方式配置GPIO为数字端口 */
#define HAL_GPIO_ADS(port, pin) ((port->PAADS) &= ~(1UL << (pin)))

/* @brief 获取GPIO寄存器名称及管脚编号 */
#define HAL_GPIO_PORT(port, pin) (port)
#define HAL_GPIO_PIN(port, pin) (pin)

/// 以下为宏参数分解函数，无须更改。供底层驱动调用。
/* @brief 获取管脚的端口及编号分配 */
#define gpio_port(x) HAL_GPIO_PORT(x)
#define gpio_pin(x) HAL_GPIO_PIN(x)
#define gpio_pin_mask(x) (1 << HAL_GPIO_PIN(x))
/* @brief 设置管脚输入模式 */
#define gpio_set_input(x) HAL_GPIO_DIR_IN(x)
/* @brief 设置管脚输出模式 */
#define gpio_set_output(x) HAL_GPIO_DIR_OUT(x)
/* @brief 获取管脚输入电平 */
#define gpio_input_get(x) boolof(HAL_GPIO_IN_GET(x))
/* @brief 获取管脚输出电平 */
#define gpio_output_get(x) boolof(HAL_GPIO_OUT_GET(x))
/* @brief 管脚置高电平 */
#define gpio_out_H(x) HAL_GPIO_OUT_SET(x)
/* @brief 管脚置低电平 */
#define gpio_out_L(x) HAL_GPIO_OUT_RST(x)
/* @brief 管脚反转电平 */
#define gpio_out_rev(x) HAL_GPIO_OUT_REV(x)
/* @brief 管脚打开上拉 */
#define gpio_up_en(x) HAL_GPIO_PTUP_EN(x)
/* @brief 管脚关闭上拉 */
#define gpio_up_dis(x) HAL_GPIO_PTUP_DIS(x)
/* @brief 管脚打开下拉 */
#define gpio_pd_en(x) HAL_GPIO_PTPD_EN(x)
/* @brief 管脚关闭下拉 */
#define gpio_pd_dis(x) HAL_GPIO_PTPD_DIS(x)
/* @brief 管脚打开开漏 */
#define gpio_od_en(x) HAL_GPIO_PTOD_EN(x)
/* @brief 管脚关闭开漏 */
#define gpio_od_dis(x) HAL_GPIO_PTOD_DIS(x)
/// @brief 管脚打开高驱动能力
#define gpio_dr_en(x) HAL_GPIO_PTDR_EN(x)
/// @brief 管脚关闭高驱动能力
#define gpio_dr_dis(x) HAL_GPIO_PTDR_DIS(x)

/* @brief 管脚设置为模拟端口 */
#define gpio_ain(x) HAL_GPIO_AIN(x)
/* @brief 管脚设置为数字端口 */
#define gpio_ads(x) HAL_GPIO_ADS(x)

/// @brief 配置GPIO外部中断定义，需要时增加定义，不要删除
#define IRQ_PORT_B04 true
#define IRQ_PORT_B05 true

#define IRQ_PORT_F00 true
#define IRQ_PORT_F01 true
/* @brief 外部中断服务函数枚举 */
typedef enum
{
#if IRQ_PORT_B04
    TYPE_PB04 = 0,    // PB04
#endif
#if IRQ_PORT_B05
    TYPE_PB05,    // PB05
#endif
#if IRQ_PORT_F00
    TYPE_PF00,    // PF00
#endif
#if IRQ_PORT_F01
    TYPE_PF01,    // PF01
#endif
    TYPE_EXTI_NUM,
} GPIO_EXTI_TYPE;

typedef enum
{
    GPIO_OPEN,
    GPIO_CLOSE,
    GPIO_MONITOR,
} GPIO_INIT_TYPE_t;

/* Exported functions ------------------------------------------------------- */
struct hal_gpio_t
{
    /// @brief  串口GPIO配置
    void (*uart_init)(uint8_t com);

    /// @brief  GPIO配置
    void (*init)(void);

    /// @brief  GPIO低功耗下初始化
    void (*init_nopower)(void);

    /// @brief  输出脉冲模式设置
    void (*pulse_out_mode)(uint8_t mode);

    /// @brief IO口扫描，typ=0只扫描电源检测IO口，typ=1扫描所有IO口
    void (*monitor)(uint8_t typ);

    /// @brief 外部中断服务函数设置
    void (*exti_set)(uint8_t irq, void func(void));

    /// @brief lcd GPIO初始化
    void (*ext_lcd)(GPIO_INIT_TYPE_t typ);

    /// @brief FLASH GPIO初始化
    void (*data_flash)(GPIO_INIT_TYPE_t type);

    /// @brief eeprom GPIO初始化
    void (*ext_eeprom)(GPIO_INIT_TYPE_t type);

    /// @brief 计量芯片GPIO初始化
    void (*mic_init)(GPIO_INIT_TYPE_t type);

    /// @brief 远程通讯模组GPIO初始化
    void (*remote_module)(GPIO_INIT_TYPE_t type);
};
extern const struct hal_gpio_t hal_gpio;

#endif /* __HAL_GPIO_H */

/** @} */
/** @} */
// #ifdef __cplusplus
// }
// #endif
