/******************************************************************************
 *    (c) Copyright 2016, SheWei Electronics Co.,Ltd.
 *    All Rights Reserved
 *
 *    Filename:      comm_phy.c
 *    Describe:      显示服务模块
 *
 *    Device:
 *    Compiler:
 *
 *    Created on:
 *    Modify record:
 *
 *******************************************************************************/
#include "comm_phy.h"
#include "ble.h"
#include "debug.h"

static COMM_STATUS_TYPE comm_phy_status;

/// @brief 设置状态
/// @param status
static void comm_status_set(COMM_STATUS_TYPE status)
{
    comm_phy_status |= status;
}

/// @brief 查询状态,并清除状态
/// @param status
/// @return 状态是否存在
bool comm_status_query(COMM_STATUS_TYPE status)
{
    bool ret;
    ret = boolof(comm_phy_status & status);
    comm_phy_status &= ~status;
    return ret;
}
/// @brief 初始化指定通道，
/// @param chn
/// @param rxbuf
/// @param bufsize
/// @param baude
void comm_init(uint8_t chn, uint8_t *rxbuf, uint16_t bufsize, uint8_t baude)
{
    baude += BAUDE_300BPS;
    switch(chn)
    {
#ifdef COM_IR
        case PHY_CHN_1:                                    // 远红外必须在通道1
            while(!hal_uart.send_over_query(COM_IR)) {}    // 300bps,此处死等可能看门狗复位
            hal_uart.open(COM_IR, UC_IR_38K, CHAR_8N1, (HAL_UART_BAUDE_TYPE)baude, rxbuf, bufsize);
            break;
#endif

#if HW_BLE_ENABLE
        case PHY_CHN_2:    // 蓝牙通道
            ble.init(rxbuf, bufsize);
            break;
#endif

#if defined(COM_MODULE)
        case PHY_CHN_3:    // 通讯模块通道, 此通道PLC和GPRS共用一个串口
        #if HW_DCU_MODUL
            // gprs_4g.init(&gprs_4g, rxbuf, bufsize);
        #endif
            break;
#endif

#if defined(COM_RS4851)
        case PHY_CHN_4:    // 4851
            hal_uart.open(COM_RS4851, UC_RS485_DE, CHAR_8E1, (HAL_UART_BAUDE_TYPE)baude, rxbuf, bufsize);
            break;
#endif

#if defined(COM_RS4852)
        case PHY_CHN_5,      // 4852
        hal_uart.open(COM_RS4852, UC_RS4852_DE, CHAR_8E1, (HAL_UART_BAUDE_TYPE)baude, rxbuf, bufsize);
        break;
#endif
    }
}
/// @brief 接收数据
/// @param chn 
/// @return 接收到的数据长度，如果是BLE, GPRS, PLC等模块，则在模块中处理后
///         确认有协议数据才给出长度，否则返回0。
uint16_t comm_recv(uint8_t chn)
{
    uint16_t len = 0;
    switch(chn)
    {
#ifdef COM_IR
        case PHY_CHN_1:  // 远红外必须在通道1
            len = hal_uart.recv(COM_IR);
            if(len) comm_status_set(COMM_STATUS_IR_RECV);
            break;
#endif

#if HW_BLE_ENABLE
        case PHY_CHN_2:    // 蓝牙通道
            len = ble.recv();
            if(len) comm_status_set(COMM_STATUS_BLE_RECV);
            break;
#endif

#if defined(COM_MODULE)
        case PHY_CHN_3:    // 通讯模块通道, 此通道PLC和GPRS共用一个串口
        #if HW_DCU_MODUL
            // len = gprs_4g.recv(&gprs_4g);
        #endif
            if(len) comm_status_set(COMM_STATUS_MODULE_RECV);
            break;
#endif

#ifdef COM_RS4851
        case PHY_CHN_4:    // 4851
            len = hal_uart.recv(COM_RS4851);
            if(len) comm_status_set(COMM_STATUS_RS4851_RECV);
            // if(len) dprintf("RS4851 recv:%d\r\n", len);
            break;
#endif

#if defined(COM_RS4852)
        case PHY_CHN_5,      // 4852
        len = hal_uart.recv(COM_RS4852);
        if(len) comm_status_set(COMM_STATUS_RS4852_RECV);
        break;
#endif
    }
    return len;
}
/// @brief 发送数据
/// @param chn 通道
/// @param msg 数据
/// @param len 数据长度
void comm_send(uint8_t chn, uint8_t* msg, uint16_t len)
{
    switch(chn)
    {
#ifdef COM_IR
        case PHY_CHN_1:  // 远红外必须在通道1
            hal_uart.send(COM_IR, msg, len);
            break;
#endif

#if HW_BLE_ENABLE
        case PHY_CHN_2:    // 蓝牙通道
            ble.send(msg, len);
            break; 
#endif

#if defined(COM_MODULE)
        case PHY_CHN_3:    // 通讯模块通道, 此通道PLC和GPRS共用一个串口
            // gprs.send(msg, len);
            // plc.send(msg, len);
            break;
#endif

#if defined(COM_RS4851)
        case PHY_CHN_4:    // 4851
            hal_uart.send(COM_RS4851, msg, len);
            break;
#endif

#if defined(COM_RS4852)
        case PHY_CHN_5,      // 4852
        hal_uart.send(COM_RS4852, msg, len);
        break;
#endif
    }
}

/// @brief 获取随机数
/// @param val 用于接收随机数
/// @param num 个数
void comm_random_bytes_get(uint8_t* val, uint8_t num)
{

}

/// @brief 物理层接口定义
const struct comm_phy_s comm_phy =
{
    .init               = comm_init,
    .recv               = comm_recv,
    .send               = comm_send,
    .random_bytes_get   = comm_random_bytes_get,
    .state_query        = comm_status_query,
};
