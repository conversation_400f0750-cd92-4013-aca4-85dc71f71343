/********************************************************************************
* @file    payment.c
* <AUTHOR> @date    2024
* @brief   预付费模块
*
******************************************************************************
*
* @note
* Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/
#include "app.h"
#include "payment.h"
#include "step_tariff.h"
#include "tariff.h"
#include "energy.h"
#include "datastore.h"
#include "mic.h"
#include "debug.h"

#define PAYMENT_CRC16               0       ///只能在测试阶段修改，产品发布后不得再修改！！！！否则会丢失数据
#define CRC16_CHK(struct,len)       STRUCT_CRC16_CHK(PAYMENT_CRC16, struct, len)
#define CRC16_CAL(struct,len)       STRUCT_CRC16_GET(PAYMENT_CRC16, struct, len)
  
#define PAY_PARA_ADDR               nvm_addr(NVM_PAYMENT_PARA)
#define PAY_DATA_ADDR               nvm_addr(NVM_PAYMENT_DATA)
#define PAY_DATA_BAK_ADDR           nvm_addr(NVM_PAYMENT_DATA_BAK)
#define PAY_DATA_PD_ADDR            nvm_addr(NVM_PAYMENT_PD)

#define PAY_PRICE_GET()             step_tariff.price_get()         ///获取当前电价,混合电价
#define PAY_STEP_PRICE_GET()        step_tariff.step_price_get()    ///获取当前阶梯电价
#define PAY_TARIFF_PRICE_GET()      step_tariff.tf_price_get()      ///获取当前时段电价
#define PAY_STEP_GET()              step_tariff.current_step_get()  ///获取当前阶梯号
#define PAY_TARIFF_GET()            tariff.cur_rate_get()           ///获取当前时段号


#define RAM_CS_VALUE                0x5AA5
#define PWRDWN_PAYMENT_FLAG         0xA55A

#define PAY_1_UNIT                  (int32)(STEP_TARIFF_ENERGY_SCALER * PAY_RATIO)  // 定义1个价格单位 

static const uint16_t pay_ratio_tab[] = {1, 10, 100, 1000, 10000};

extern const pay_para_s  pay_default_para; ///默认参数
static const pay_para_s* pay_running_para; ///运行参数，指向codeflash

/* Private variables ---------------------------------------------------------*/
static pay_account_s pay_account;              // 预付费相关数据
static pay_data_s    pay_data;                 // 付费相关掉电保存数据
static TYPE_PAY_STUS pay_out_stus;

/* Private function prototypes -----------------------------------------------*/
bool pay_para_set(uint16_t ofst, const void* val, uint16_t len);

/* Private functions ---------------------------------------------------------*/
/// @brief 参数计算检验后存入NVM中
/// @param ofst
/// @param val
/// @param len
/// @return
static bool pay_para_save(uint16_t ofst, const void* val, uint16_t len)
{
    pay_para_s para;
    if(ofst != 0) memcpy(&para, pay_running_para, sizeof(para));
    memcpy((uint8_t*)&para + ofst, val, len);
    CRC16_CAL(&para, sizeof(pay_para_s));
    pay_running_para = (const pay_para_s*)PAY_PARA_ADDR;
    return nvm.write((uint32_t)pay_running_para, &para, sizeof(pay_para_s));
}

/// @brief 参数指针初始化并检验参数
/// @param
static void pay_para_load(void)
{
    pay_running_para = (const pay_para_s*)PAY_PARA_ADDR;
    if(CRC16_CHK(pay_running_para, sizeof(pay_para_s)) == false)
    {
        pay_running_para = &pay_default_para;
    }
}

/// @brief 数据计算检验后存入NVM
/// @param wr_nvm 是否写NVM
/// @return
static bool pay_data_save(bool wr_nvm)
{
    CRC16_CAL(&pay_data, sizeof(pay_data_s));
    if(wr_nvm)
    {
        nvm.write(PAY_DATA_ADDR,     &pay_data, sizeof(pay_data_s));
        nvm.write(PAY_DATA_BAK_ADDR, &pay_data, sizeof(pay_data_s));
    }
    return true;
}

/// @brief 数据初始化并检验
/// @param type 0-上电 1-秒任务
static void pay_data_load(uint8_t type)
{
    switch(type)
    {
        case 0: ///上电初始化检出 
        if(CRC16_CHK(&pay_data, sizeof(pay_data_s)) ==  false)
        {
            /* 读取FLASH存储的总及分费率总电能 */
            nvm.read(PAY_DATA_PD_ADDR, &pay_data, sizeof(pay_data_s));
            if(pay_data.flag != PWRDWN_PAYMENT_FLAG || (CRC16_CHK(&pay_data, sizeof(pay_data_s)) ==  false))
            { // 说明该FLASH中的预付费数据是未正确掉电保存过
                nvm.read(PAY_DATA_ADDR, &pay_data, sizeof(pay_data_s));
                if(CRC16_CHK(&pay_data, sizeof(pay_data_s)) ==  false)
                {
                    nvm.read(PAY_DATA_BAK_ADDR, &pay_data, sizeof(pay_data_s));
                    if(CRC16_CHK(&pay_data, sizeof(pay_data_s)) ==  false)
                    {
                        // 付费相关数据错误
                        memset(&pay_data, 0, sizeof(pay_data_s));
                        CRC16_CAL(&pay_data, sizeof(pay_data_s));
                    }
                }
                pay_data.flag = RAM_CS_VALUE;
            }
        }

        case 1: ///秒任务周期校验
        if(CRC16_CHK(&pay_data, sizeof(pay_data_s)) ==  false)
        {
            nvm.read(PAY_DATA_ADDR, &pay_data, sizeof(pay_data_s));
            if(CRC16_CHK(&pay_data, sizeof(pay_data_s)) ==  false)
            {
                nvm.read(PAY_DATA_BAK_ADDR, &pay_data, sizeof(pay_data_s));
                if(CRC16_CHK(&pay_data, sizeof(pay_data_s)) ==  false)
                {
                    nvm.read(PAY_DATA_PD_ADDR, &pay_data, sizeof(pay_data_s));
                    if(CRC16_CHK(&pay_data, sizeof(pay_data_s)) ==  false)
                    {
                        // 付费相关数据错误
                        memset(&pay_data, 0, sizeof(pay_data_s));
                        pay_data.flag = RAM_CS_VALUE;
                        CRC16_CAL(&pay_data, sizeof(pay_data_s));
                    }
                }
            }
        }
    }
}

/// @brief 组合数据更新
/// @param ptr_gd 
static void gd_update(group_data_s *ptr_gd)
{
    int32_t tmp_data;

    tmp_data = ptr_gd->remain / PAY_RATIO;
    ptr_gd->remain %= PAY_RATIO;
    ptr_gd->data += tmp_data;

    if(ptr_gd->remain < 0)
    {
        if(ptr_gd->data > 0)
        {
            ptr_gd->data--;
            ptr_gd->remain += PAY_RATIO;
        }
    }
}

/// @brief 组合数据设置
/// @param ptr_gd 
/// @param data 
static void gd_set(group_data_s *ptr_gd, int32_t data)
{
    ptr_gd->remain =  0;
    ptr_gd->data = data;
}

/// @brief 组合数据增加处理.
/// @param ptr_gd = gd1 + gd2
static void gd_add(group_data_s *ptr_gd, group_data_s gd1, group_data_s gd2)
{
    ptr_gd->remain = gd1.remain + gd2.remain;
    ptr_gd->data   = gd1.data   + gd2.data;
    gd_update(ptr_gd);
}

/// @brief  组合数据扣减处理.(---gd1必须大于等于gd2---)
/// @param  ptr_gd = gd1 - gd2
/// @return None
static void gd_sub(group_data_s *ptr_gd, group_data_s gd1, group_data_s gd2)
{
    ptr_gd->remain = gd1.remain - gd2.remain;
    ptr_gd->data   = gd1.data   - gd2.data;
    gd_update(ptr_gd);
}

/// @brief  判断组合数据是否为0.
/// @param  None
/// @return None
static bool gd_is_zero(group_data_s gd)
{
    return ((gd.remain == 0) && (gd.data == 0)) ? true : false;
}

/// @brief  组合数据比较.
/// @param  gd1-组合数据1， gd2-组合数据2
/// @return "0" - (gd1 == gd2)
///         "1" - (gd1 >  gd2)
///        "-1" - (gd1 <  gd2)
static int8_t gd_compare(group_data_s gd1, group_data_s gd2)
{
    gd_update(&gd1);
    gd_update(&gd2);

    if(gd1.data > gd2.data) { return 1; }
    else if(gd1.data == gd2.data)
    {
        if(gd1.remain > gd2.remain) { return 1; }
        else if(gd1.remain == gd2.remain) { return 0; }
        else { return -1; }
    }
    else { return -1; }
}

/// @brief  获取秒电能增量
/// @param  None
/// @return 返回秒增量电能，单位 1 wh
static uint16_t pay_second_energy_get(void)
{
    ENERGY_DEF_FORMAT sec_en;
    ENERGY_DEF_FORMAT eng = energy.phs_cum_value_get(T_PHASE, PAYT_ACCOUNT_ENERGY, 0);

    sec_en = eng - pay_data.lst_sec_en_bk;
    pay_data.lst_sec_en_bk = eng;
    pay_data_save(false);

    if(sec_en > EN_MAX_ENERGY_PER_SECOND * 15 * 60) // 增量异常大于15分钟的增量电能
        return 0; 
    else 
        return (uint16_t)(sec_en * STEP_TARIFF_ENERGY_SCALER); // 将电能单位化为1wh 
}

/// @brief  获取当前电价
/// @param  None
/// @return 0.0001元
static uint32_t pay_price_get(void)
{
    return PAY_PRICE_GET();
}

/// @brief  电能消耗处理
/// @param  None
/// @return None
static void pay_consume(void)
{
    uint32_t cur_en, last_step_en;
    group_data_s delta_unit;
    uint32_t price = 0;
    uint16_t cur_consume = 0, lst_consume = 0;
    uint8_t  cur_step;

    DBG_PRINTF(P_PAY, T);
    cur_consume = pay_second_energy_get();
    if(cur_consume > 0)
    {
        price = pay_price_get(); 
        if(price == 0) return;
        cur_step = PAY_STEP_GET();
        if(pay_data.last_step  == 0 || pay_data.last_step > STEP_TARIFF_NUM + 1) pay_data.last_step = 1;
        if(pay_data.last_price == 0 || cur_step == 1) pay_data.last_price = price;
        
        if(pay_data.last_step < cur_step)  // 阶梯更新
        {  
            last_step_en = step_tariff.energy_get(pay_data.last_step) * (int)(STEP_TARIFF_ENERGY_SCALER);

            cur_en = step_tariff.inc_energy_get() * (int)(STEP_TARIFF_ENERGY_SCALER);
            last_step_en = step_tariff.energy_get(cur_step) * (int)(STEP_TARIFF_ENERGY_SCALER);
            uint16_t tmp = cur_en - last_step_en;
            lst_consume  = cur_consume - tmp;
            cur_consume  = tmp;
        }
        
        pay_data.remain_paid += (price * cur_consume + pay_data.last_price * lst_consume);
        if(pay_data.remain_paid >= PAY_1_UNIT) // 大于1个价格单位，更新一次余额
        {
            delta_unit.remain = pay_data.remain_paid / PAY_1_UNIT;
            delta_unit.data = 0;
            pay_data.remain_paid %= PAY_1_UNIT;

            // 剩余信用扣减
            gd_sub(&pay_data.residual_credit, pay_data.residual_credit, delta_unit);

            // 增加累计消费额度
            gd_add(&pay_data.total_amount_paid, pay_data.total_amount_paid, delta_unit);
        }
        pay_data.last_step = cur_step;
        pay_data.last_price= price;
        pay_data_save(false);
    }

    DBG_PRINTF(P_PAY, T);
    DBG_PRINTF(P_PAY, D, "\r\n\r\n payment end");
    //DBG_PRINTF(P_PAY, D, "\r\n cum:%d ",   cum); 
    DBG_PRINTF(P_PAY, D, "\r\n pay_data.total_amount_paid.data:%d ",   pay_data.total_amount_paid.data);  
    DBG_PRINTF(P_PAY, D, "\r\n pay_data.total_amount_paid.remain:%d ", pay_data.total_amount_paid.remain); 
    DBG_PRINTF(P_PAY, D, "\r\n pay_data.residual_credit.data:%d ",     pay_data.residual_credit.data); 
    DBG_PRINTF(P_PAY, D, "\r\n pay_data.residual_credit.remain:%d ",   pay_data.residual_credit.remain); 
    DBG_PRINTF(P_PAY, D, "\r\n pay_data.remain_paid :%d ",             pay_data.remain_paid ); 
}

/// @brief  更新付费相关数据.
/// @param  None
/// @return None
static void pay_update_data(void)
{
    group_data_s gd_zero = {0, 0};
    group_data_s gd_tmp;

    gd_add(&gd_tmp, pay_account.current_credit_amount, pay_account.current_credit_amount_fixed);
    if(gd_compare(gd_tmp, gd_zero) > 0)
    {
        pay_account.available_credit = gd_tmp;
    }
    else
    {
        pay_account.available_credit = gd_zero;
    }

    if(gd_compare(pay_data.residual_credit, gd_zero) > 0)
    {
        pay_account.amount_to_clear = gd_zero;
    }
    else
    {
        pay_account.amount_to_clear = pay_data.residual_credit;
    }

}

/// @brief  更新当前信用状态
/// @param  None
/// @return None
static void pay_update_cur_credit_status(void)
{
    pay_account.status.val = 0;

    /* 更新状态字 */
    if(gd_compare(pay_data.residual_credit, pay_running_para->warning_threshold1) <= 0)
    {
        pay_account.status.low_credit1 = 1;
    }
    if(gd_compare(pay_data.residual_credit, pay_running_para->warning_threshold2) <= 0)
    {
        pay_account.status.low_credit2 = 1;
    }

}

/// @brief  付费单元状态机处理.
/// @param  msg - 预付费相关消息
///         dat - 数据缓存
///         len - 数据长度
/// @return None
static void pay_state_machine(void)
{
    pay_update_data();
    pay_update_cur_credit_status();
}

/// @brief  付费数据保存,再电能模块调用，跟电量在同一时间保存保存
/// @param  None
/// @return None
static void pay_data_save_callback(void)
{
    pay_data_save(true);
}

/// 以下为接口函数********************************************

/// @brief  付费单元初始化处理。
/// @param  None
/// @return None
void pay_init(void)
{
    pay_para_load();
    pay_data_load(0); 

    if(bsp.state_query(STUS_BSP_PWR_ON))  // 掉电运行无需执行下面的相关动作
    {
        if(pay_data.flag == PWRDWN_PAYMENT_FLAG)
        {   // 说明该FLASH中的预付费数据有正确掉电保存过
            uint16_t crc = 0;
            pay_data.flag = RAM_CS_VALUE;
            nvm.read(PAY_DATA_ADDR + member_offset(pay_data_s, crc), &crc, 2);
            if(crc != pay_data.crc) nvm.write(PAY_DATA_ADDR, &pay_data, sizeof(pay_data_s));  // 刷新EEPROM中付费数据
            nvm.write(PAY_DATA_PD_ADDR + member_offset(pay_data_s, flag), &pay_data.flag, 2); // 清除掉电存储标志
        }
        else
        {
            pay_data.flag = RAM_CS_VALUE;
        }

        memset(&pay_account, 0, sizeof(pay_account_s));
        pay_out_stus = 0;

        /* 当电能保存时调用保存预付费数据函数指针 */
        energy.checkin_callback(pay_data_save_callback);
    }
}

/// @brief  付费单元数据默认参数设置。
/// @param  None
/// @return None
void pay_reset(uint8_t type)
{
    if(type & SYS_PARA_RESET)
    {
        pay_para_save(0, &pay_default_para, sizeof(pay_para_s));
    }

    if(type & SYS_DATA_RESET)
    {
        memset(&pay_data, 0, sizeof(pay_data_s));
        pay_data.flag = RAM_CS_VALUE;
        pay_data_save(boolof(type != SYS_GLOBAL_RESET));

        memset(&pay_account, 0, sizeof(pay_account_s));
        pay_out_stus = 0;
    }
}

/// @brief  付费单元数据掉电保存。
/// @param  None
/// @return None
void pay_power_down_save(void)
{
    pay_consume();
    /* 只保存一份数据，备份数据可留作备用恢复 */
    pay_data.flag = PWRDWN_PAYMENT_FLAG;
    nvm.write(PAY_DATA_PD_ADDR, &pay_data, sizeof(pay_data_s));
}

/// @brief  付费单元秒任务处理.
/// @param  None
/// @return None
void pay_second_run(void)
{
    pay_para_load();
    pay_data_load(1);

    /*=======以下为预付费秒任务处理=======*/
    pay_consume();
    /* 预付费报警灯处理 */
    
}

/// @brief  付费单元空闲任务处理
/// @param  None
/// @return None
void pay_idle_run(void)
{
    /* 状态机处理 */
    pay_state_machine();
    //pay_data_save(false);
}

/// @brief  预购电判断。(该函数必须提供模块使用), 注意充值单位与预付费模块单位换算问题
/// @param  data - 充值金额/电量，单位0.01
/// @return 返回预购电结果，true - 成功，false - 失败
bool pay_pre_top_up(int32_t data)
{
    group_data_s tmp_data;
    group_data_s max_data;
    int32_t thd;

    // 不允许充值负值
    if(data < 0) return false;

    data = (int32_t)(data * (PAY_CURRENCY_SCALE_2 / PAY_RATIO));
    gd_set(&tmp_data, data);

    // 单次购电门限判断
    thd = (int32_t)(pay_running_para->max_vend);
    gd_set(&max_data, thd);
    if(gd_compare(tmp_data, max_data) >= 0) return false;

    // 囤积门限判断
    thd = (int32_t)(pay_running_para->max_credit);
    gd_set(&max_data, thd);
    gd_add(&tmp_data, tmp_data, pay_data.residual_credit);
    if(gd_compare(tmp_data, max_data) > 0) return false;

    return true;
}

/// 数据访问接口

/// @brief  购电处理。
/// @param  data - 充值金额/电量, 单位0.01
/// @return 返购电结果，true - 成功，false - 失败
bool pay_top_up(int32_t data)
{
    group_data_s tmp_data;

    data = (int32_t)(data * (PAY_CURRENCY_SCALE_2 / PAY_RATIO));
    gd_set(&tmp_data, data);
    gd_add(&pay_data.residual_credit, pay_data.residual_credit, tmp_data);
    pay_data.total_purchase_credit += data;
    pay_data.last_purchase_credit  =  data;
    pay_data_save(true);
    pay_out_stus |= STUS_TOP_UP;
    return true;
}

/// @brief  退费处理。
/// @param  data - 退费金额/电量, 单位0.01
/// @return 返购电结果，true - 成功，false - 失败
bool pay_deduct(int32_t data)
{
    group_data_s tmp_data;

    data = (int32_t)(data * (PAY_CURRENCY_SCALE_2 / PAY_RATIO));
    gd_set(&tmp_data, data);
    gd_sub(&pay_data.residual_credit, pay_data.residual_credit, tmp_data);
    pay_data.total_purchase_credit -= data;
    pay_data.last_purchase_credit  =  data;
    pay_data_save(true);
    pay_out_stus |= STUS_DEDUCT;
    return true;
}

/// @brief  清余额
bool pay_amount_clr(void)
{
    memset(&pay_data.residual_credit, 0, sizeof(group_data_s));
    pay_data.credit_status.val = 0; // 
    pay_account.status.val = 0;
    pay_data_save(true);
    return true;
}

/// @brief  获取当前账户信息 
pay_account_s *pay_account_get(void)
{
    return &pay_account;
}

/// @brief  获取付费模块数据
pay_data_s *pay_data_get(void)
{
    return &pay_data;
}

/// @brief 获取付费模块参数
const pay_para_s *pay_para_get(void)
{
    return pay_running_para;
}

/// @brief  设置付费模块参数
bool pay_para_set(uint16_t ofst, const void* val, uint16_t len)
{
    pay_out_stus |= STUS_PAY_PROGRAM;
    return pay_para_save(ofst, val, len);
}

/// @brief  获取默认日均消费
PAY_CREDIT_DATA_TYPE pay_default_daily_paid_get(void)
{
    float val = 0;
    uint32_t day_en = (uint32_t)PHASE_NUM * BASE_CURRENT * INPUT_VOLTAGE * 24 / 10; // 按IB/10,UN计算的日耗电能，单位wh
    val = pay_price_get() * PAY_RATIO * 1.0;
    return (PAY_CREDIT_DATA_TYPE)(val * day_en / PAY_1_UNIT);
}

/// @brief  获取以当前功率消耗速率，每小时对应消耗的信用
/// @param  None
/// @return None
PAY_CREDIT_DATA_TYPE pay_avg_hour_consum_get(void)
{
    float hour_en;
    float power;

#if defined(POLYPHASE_METER)
    power = mic.ins->pwr_abs[0];
#else
    power = fabs(mic.ins->pwr_p[0]);
#endif
    hour_en = power * pay_price_get() * PAY_RATIO;
    return (PAY_CREDIT_DATA_TYPE)(hour_en / 1000);
}

/// @brief 获取付费模块输出的各状态
bool pay_state_query(TYPE_PAY_STUS state)
{
    return boolof(pay_out_stus & state);
}

/// @brief 清除付费模块输出的各状态
void pay_state_clr(void)
{
    pay_out_stus = 0;
}


/// @brief 声明预付费模块对象
const struct payment_t pay =
{
    .reset                  = pay_reset,
    .state_query            = pay_state_query,
    .state_clr              = pay_state_clr,
    .para_get               = pay_para_get,
    .para_set               = pay_para_set,
    .data_get               = pay_data_get,

    .pre_top_up             = pay_pre_top_up,
    .top_up                 = pay_top_up,
    .deduct                 = pay_deduct,
    .amount_clr             = pay_amount_clr,
    .account_get            = pay_account_get,
    .current_price_get      = pay_price_get,
    .default_daily_paid_get = pay_default_daily_paid_get,
    .avg_hour_consum_get    = pay_avg_hour_consum_get,
};

/// @brief 声明预付费模块任务接口
const struct app_task_t payment_task =
{
    .init                   = pay_init,
    .second_run             = pay_second_run,
    .idle_run               = pay_idle_run,
    .power_down_save        = pay_power_down_save,
    .power_off_init         = pay_init,
};

// end of payment.c
