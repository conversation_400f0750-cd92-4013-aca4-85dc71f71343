/**
 ******************************************************************************
 * @file    remote_module.c
 * <AUTHOR> @date    2025
 * @brief
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2025  SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/
#include "remote_module.h"
#include "debug.h"

#if (REMOTE_MODULE_TYPE == M_ML307R_DL)
#include "ML307R.c"
#elif
#error "不支持的模块类型!"
#endif

const struct remote_module_s remote_m = 
{
    .state           = &r_state,
    .init            = r_module_init,
    .recv            = r_module_recv,
    .send            = r_module_send,
    .lastgasp_send   = r_module_lastgasp_send,
    .access_request  = r_module_access_request,
    .task_state      = r_module_task_get,
    .send_over_query = r_uart_send_over_query,
};

//
