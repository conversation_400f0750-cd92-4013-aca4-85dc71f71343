/**
 ******************************************************************************
 * @file    WS8201.c
 * <AUTHOR> @date    2024
 * @brief   蓝牙芯片WS8201驱动，配置和透传功能。 有不符合流程的特殊处理，移植不建议使用此模板。
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/
#include "pt.h"
#include "ble.h"
#include "bsp.h"
#include "debug.h"
#include "api.h"

/* Private typedef -----------------------------------------------------------*/
#define BT_CMD 0x01
#define BT_EVENT 0x02

typedef enum
{
    HCI_EVENT_BT_CONNECTED      = 0x00,    // BT3.0 连接建立
    HCI_EVENT_BLE_CONNECTED     = 0x02,    // BLE 连接建立
    HCI_EVENT_BT_DISCONNECTED   = 0x03,    // BT3.0 连接断开
    HCI_EVENT_BLE_DISCONNECTED  = 0x05,    // BLE 连接断开
    HCI_EVENT_CMD_COMPLETE      = 0x06,    // 命令已完成
    HCI_EVENT_SPP_DATA_RECEIVED = 0x07,    // 接收到 BT3.0 数据 （SPP）
    HCI_EVENT_BLE_DATA_RECEIVED = 0x08,    // 接收到 BLE 数据
    HCI_EVENT_I_AM_READY        = 0x09,    // 模块准备好
    HCI_EVENT_STAUS_RESPONSE    = 0x0A,    // 状态回复
    HCI_EVENT_NVRAM_CHANGED     = 0x0D,    // 上传 NVRAM 数据
    HCI_EVENT_UART_EXCEPTION    = 0x0F,    // HCI 包格式错误
    HCI_EVENT_GKEY              = 0x0E,    // 发送 Numeric Comparison 配 对方式中产生的密钥
    HCI_EVENT_GET_PASSKEY       = 0x10,    // PASSKEY 配对方式中通 知MCU 返回密钥
    HCI_EVENT_PAIRING_COMPLETED = 0x11,    // 配对完成的事件，通知 MCU 配对完成
    HCI_EVENT_REMOTE_MTU        = 0x12,
    HCI_EVENT_POWER_AND_ERROR   = 0xFC
} BT_EVENT_t;

typedef enum bt_cmd_enum
{
    HCI_CMD_SET_BT_ADDR      = 0x00,    // 设置 BT3.0 地址
    HCI_CMD_SET_BLE_ADDR     = 0x01,    // 设置 BLE 地址
    HCI_CMD_SET_VISIBILITY   = 0x02,    // 设置可发现和广播
    HCI_CMD_SET_BT_NAME      = 0x03,    // 设置 BT3.0 名称
    HCI_CMD_SET_BLE_NAME     = 0x04,    // 设置 BLE 名称
    HCI_CMD_SEND_SPP_DATA    = 0x05,    // 发送 BT3.0（SPP） 数据
    HCI_CMD_SEND_BLE_DATA    = 0x09,    // 发送 BLE 数据
    HCI_CMD_STATUS_REQUEST   = 0x0B,    // 请求蓝牙状态
    HCI_CMD_SET_PAIRING_MODE = 0x0C,    // 设置配对模式
    HCI_CMD_SET_PINCODE      = 0x0D,    // 设置配对码
    HCI_CMD_SET_UART_FLOW    = 0x0E,    // 设置 UART 流控
    HCI_CMD_SET_UART_BAUD    = 0x0F,    // 设置 UART 波特率
    HCI_CMD_VERSION_REQUEST  = 0x10,    // 查询模块固件版本
    HCI_CMD_BT_DISCONNECT    = 0x11,    // 断开 BT3.0 连接
    HCI_CMD_BLE_DISCONNECT   = 0x12,    // 断开 BLE 连接
    HCI_CMD_SET_COD          = 0x15,    // 设置 COD
    HCI_CMD_SET_NVRAM        = 0x26,    // 下发 NV 数据
    HCI_CMD_ENTER_SLEEP_MODE = 0x27,    // 进入休眠模式
    HCI_CMD_CONFIRM_GKEY     = 0x28,    // Numeric Comparison 配 对方式中对密钥的比较
    HCI_CMD_SET_ADV_DATA     = 0x2A,    // 设置 ADV 数据
    HCI_CMD_POWER_REQ        = 0x2B,    // 查询模块电源电压
    // HCI_EVENT_GET_PASSKEY =0x30,   // 用于 PASSKEY 配对方式 中对密钥的比较
    HCI_CMD_SET_GPIO  = 0x31,    // 设置 GPIO
    HCI_CMD_READ_GPIO = 0x32,    // 读取 GPIO 设置
} BT_CMD_t;

typedef enum
{
    BLE_INIT_STATUS,
    BLE_CFG_STATUS,    // 参数配置阶段
    // BLE_CONNEECT,           // 等待连接
    BLE_TRANSFER_STATUS,    // 数据传输阶段
} BLE_STATUS;

typedef struct
{
    struct pt atpt, trypt;    /// 子线程号
    uint16_t  rx_len;         /// 接收消息总长度
    uint16_t  bufsize;        /// 接收缓冲大小
    octstr_t  rxbuf;          /// 接收器，面向uart

    uint8_t *msg;            /// 面向数据应用层的数据buff
    uint16_t msg_maxsize;    /// 接收缓冲大小
    uint16_t msg_len;        ///
    void    *param;          /// 命令参数
    uint8_t  error_code;     /// 错误码
    uint8_t  lock;           /// 接收时，防止互斥
    uint8_t  last_cmd;       /// 发送的指令
    bool     ret;
    bool     ready;      /// 模块OK
    bool     poweron;    /// 模块上电
    bool     conn;       /// 连接状态
} ble_chn_t;

typedef struct
{
    struct pt  mpt;            /// 子线程号
    BLE_STATUS work_status;    /// 当前工作状态
    ble_chn_t *chn;            /// Command channel excute
} ble_sta_t;

typedef struct
{
    uint16_t (*request)(uint8_t *buf, void *param);
    bool (*parse)(const uint8_t *buf, uint16_t len);
    const uint8_t *cmd;
    uint16_t       time_out;
} ble_cmd_t;

typedef struct hci_struct
{
    uint8_t  typ;
    uint8_t  cmd;
    uint8_t  len;
    uint8_t *dat;
} hci_pack_s;

/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
static struct pt pt_ble_process;    /// 消息处理主线程号
static ble_chn_t ble_channel;
static ble_sta_t ble_state = {
    .chn         = &ble_channel,
    .work_status = BLE_INIT_STATUS,
};

static uint8_t bt_catch_buff[256];
static uint8_t com_rx_buf[256];    /// 用于BLE内部串口收发缓冲 -> 需根据协议调整
static uint8_t com_tx_buf[256];

/* Private constants ---------------------------------------------------------*/
static uint16_t create_general(uint8_t *buf, void *param);
static uint16_t create_set_bt_name(uint8_t *buf, void *param);
static uint16_t create_set_bt_addr(uint8_t *buf, void *param);
static bool     parse_bt_poweron(const uint8_t *buf, uint16_t len);
static bool     parse_bt_ready(const uint8_t *buf, uint16_t len);
static bool     parse_bt_cmd_complete(const uint8_t *buf, uint16_t len);

const uint8_t bt_patch1[]  = {0x04, 0x6E, 0x02, 0x00, 0x02};
const uint8_t bt_patch2[]  = {0x04, 0x8A, 0x02, 0x07, 0x08};
const uint8_t bt_patch3[]  = {0x0A, 0x81, 0x08, 0x56, 0x00, 0x30, 0x10, 0x74, 0x00, 0x16, 0x00};
const uint8_t bt_patch4[]  = {0x0A, 0x81, 0x08, 0x56, 0x00, 0x61, 0x90, 0x00, 0x00, 0xEF, 0x7F};
const uint8_t bt_patch5[]  = {0x06, 0x87, 0x04, 0x00, 0x01, 0xC8, 0x00};
const uint8_t bt_patch6[]  = {0x0C, 0x65, 0x0A, 0x38, 0x04, 0x12, 0x00, 0x38, 0x04, 0x12, 0x00, 0x40, 0x06};
const uint8_t bt_patch7[]  = {0x06, 0x8C, 0x04, 0x01, 0x00, 0x70, 0x17};
const uint8_t bt_patch8[]  = {0x04, 0x62, 0x02, 0x80, 0x80};
const uint8_t bt_patch9[]  = {0x11, 0x64, 0x0F, 0x01, 0x04, 0x00, 0x20, 0x03, 0x20, 0x03, 0x04, 0x00, 0x04, 0x00, 0x58, 0x02, 0x70, 0x17};
const uint8_t bt_patch10[] = {0x05, 0x67, 0x03, 0x00, 0x00, 0xFF};
const uint8_t bt_patch11[] = {0x09, 0x68, 0x07, 0x00, 0x10, 0x01, 0xFF, 0x02, 0x01, 0x02};
const uint8_t bt_patch12[] = {0x09, 0x68, 0x07, 0x00, 0x04, 0x02, 0xFF, 0x02, 0x01, 0x02};
const uint8_t bt_patch13[] = {0x13, 0x67, 0x11, 0x01, 0x49, 0x53, 0x53, 0x43, 0xFE, 0x7D, 0x4A, 0xE5, 0x8F, 0xA9, 0x9F, 0xAF, 0xD2, 0x05, 0xE4, 0x55};
const uint8_t bt_patch14[] = {0x17, 0x68, 0x15, 0x01, 0x10, 0x49, 0x53, 0x53, 0x43, 0x1E, 0x4D, 0x4B, 0xD9, 0xBA, 0x61, 0x23, 0xC6, 0x47, 0x24, 0x96, 0x16, 0x02, 0x01, 0x02};
const uint8_t bt_patch15[] = {0x17, 0x68, 0x15, 0x01, 0x0C, 0x49, 0x53, 0x53, 0x43, 0x88, 0x41, 0x43, 0xF4, 0xA8, 0xD4, 0xEC, 0xBE, 0x34, 0x72, 0x9B, 0xB3, 0x02, 0x01, 0x02};
const uint8_t bt_patch16[] = {0x02, 0x60, 0x00};

const uint8_t bt_patch20[] = {0x03, HCI_CMD_SET_VISIBILITY, 1, 4};
const uint8_t bt_patch21[] = {0x02, HCI_CMD_STATUS_REQUEST, 0};

const ble_cmd_t ble_cmd_list[] = {
    {create_general, parse_bt_poweron, NULL, 10000},    // 等待模块上电
    {create_general, parse_bt_cmd_complete, bt_patch1, 5000},
    {create_general, parse_bt_cmd_complete, bt_patch2, 5000},
    {create_general, parse_bt_cmd_complete, bt_patch3, 5000},
    {create_general, parse_bt_cmd_complete, bt_patch4, 5000},
    {create_general, parse_bt_cmd_complete, bt_patch5, 5000},
    {create_general, parse_bt_cmd_complete, bt_patch6, 5000},
    {create_general, parse_bt_cmd_complete, bt_patch7, 5000},
    {create_general, parse_bt_cmd_complete, bt_patch8, 5000},
    {create_general, parse_bt_cmd_complete, bt_patch9, 5000},
    {create_general, parse_bt_cmd_complete, bt_patch10, 5000},
    {create_general, parse_bt_cmd_complete, bt_patch11, 5000},
    {create_general, parse_bt_cmd_complete, bt_patch12, 5000},
    {create_general, parse_bt_cmd_complete, bt_patch13, 5000},
    {create_general, parse_bt_cmd_complete, bt_patch14, 5000},
    {create_general, parse_bt_cmd_complete, bt_patch15, 5000},
    {create_general, parse_bt_cmd_complete, bt_patch16, 5000},
    {create_general, parse_bt_ready, NULL, 40000},
    // {create_set_bt_addr, parse_bt_cmd_complete, NULL, 5000},
    {create_set_bt_name, parse_bt_cmd_complete, NULL, 5000},
    {create_general, parse_bt_cmd_complete, bt_patch20, 5000},
    // {create_general, parse_bt_status_request, bt_patch21},
};

/* Private function prototypes -----------------------------------------------*/

__INLINE void bt_uart_open(void)
{
    hal_uart.open(COM_BLE, UC_NONE, CHAR_8N1, BAUDE_115200BPS, com_rx_buf, sizeof(com_rx_buf));    // 打开串口,中断方式
}

__INLINE void bt_uart_send(const uint8_t *buf, uint16_t len)
{
    hal_uart.send(COM_BLE, buf, len);    // 发送数据到串口
}

/// @brief 生成通用命令
/// @param buf
/// @param param
/// @return
static uint16_t create_general(uint8_t *buf, void *param)
{
    uint8_t *cmd = (uint8_t *)param;
    uint16_t len;
    if(cmd == NULL) { return 0; }    /// 如果命令为空或长度为0，则返回0
    len = cmd[0];
    if(len == 0 || len > sizeof(com_tx_buf)) { return 0; }
    buf[0] = BT_CMD;
    memcpy(&buf[1], &cmd[1], len);
    ble_channel.last_cmd = cmd[1];
    return (len + 1);
}

/// @brief 设置device name
/// @param buf
/// @param param
/// @return
static uint16_t create_set_bt_name(uint8_t *buf, void *param)
{
    uint16_t len;
    uint8_t  temp_buf[METER_SN_LEN];    // 已经去掉长度

    len = api.meter_sn_get(temp_buf);
    if(len < 6) memset(temp_buf, 0, sizeof(temp_buf)), temp_buf[0] = 1;
    buf[0]               = BT_CMD;
    buf[1]               = HCI_CMD_SET_BLE_NAME;
    ble_channel.last_cmd = HCI_CMD_SET_BLE_NAME;
    buf[2]               = sprintf((char *)(buf + 3), "SW%02x%02x%02x%02x%02x%02x", temp_buf[5], temp_buf[4], temp_buf[3], temp_buf[2], temp_buf[1], temp_buf[0]);
    return (buf[2] + 3);
}

static uint16_t create_set_bt_addr(uint8_t *buf, void *param)
{
    buf[0]               = BT_CMD;
    buf[1]               = HCI_CMD_SET_BLE_ADDR;
    ble_channel.last_cmd = HCI_CMD_SET_BLE_ADDR;
    buf[2]               = api.meter_sn_get(buf + 3);
    return (buf[2] + 3);
}

//
static bool parse_bt_poweron(const uint8_t *buf, uint16_t len)
{
    if(ble_channel.poweron) return true;
    if(len < 3) return false;
    ble_channel.poweron = boolof(buf[0] == BT_EVENT && buf[1] == HCI_EVENT_POWER_AND_ERROR && buf[2] == 0x00);
    return ble_channel.poweron;
}

static bool parse_bt_ready(const uint8_t *buf, uint16_t len)
{
    if(ble_channel.ready) return true;
    if(len < 3) return false;
    return boolof(buf[0] == BT_EVENT && buf[1] == HCI_EVENT_I_AM_READY && buf[2] == 0x00);
}

static bool parse_bt_cmd_complete(const uint8_t *buf, uint16_t len)
{
    uint8_t    *ptr = (uint8_t *)buf;
    hci_pack_s *hci;
    uint8_t     mlen;
    bool        ret = false;

    while(len)
    {
        hci  = (hci_pack_s *)ptr;
        mlen = hci->len + 3;
        if(len < mlen) break;
        if(hci->typ == BT_EVENT)
        {
            if(mlen >= 5 && hci->cmd == HCI_EVENT_CMD_COMPLETE && hci->dat[1] == ble_channel.last_cmd && hci->dat[2] == 0) ret = true;
            if(mlen >= 3 && hci->cmd == HCI_EVENT_I_AM_READY && hci->len == 0x00) ble_channel.ready = true;
            if(mlen >= 3 && hci->cmd == HCI_EVENT_POWER_AND_ERROR && hci->len == 0x00) ble_channel.poweron = true;
        }
        memset(ptr, 0x00, mlen), ptr += mlen, len -= mlen;
    }

    return ret;
}

// static bool parse_bt_status_request(const uint8_t *buf, uint16_t len)
// {
//     if(len < 4) return false;
//     return boolof(buf[0] == BT_EVENT && (buf[1] == HCI_EVENT_STAUS_RESPONSE && buf[2] == 0x01 && (buf[3] & 0x20)) || (buf[1] == HCI_EVENT_BLE_CONNECTED && buf[2] == 0x00));
// }

static void parse_bt_connect_state(const uint8_t *buf, uint16_t len)
{
    hci_pack_s *hci = (hci_pack_s *)buf;
    uint8_t     mlen;

    if(3 == len && hci->typ == BT_EVENT && hci->len == 0)
    {
        if(hci->cmd == HCI_EVENT_BLE_CONNECTED) { ble_state.chn->conn = true; }
        else if(hci->cmd == HCI_EVENT_BLE_DISCONNECTED) { ble_state.chn->conn = false; }
    }
}

/// @brief PT线程初始化
/// @param
static void ble_pt_restart(void)
{
    PT_INIT(&ble_state.mpt);
    PT_INIT(&ble_state.chn->atpt);
    PT_INIT(&ble_state.chn->trypt);
}

/// @brief PT线程延时函数
/// @param pt
/// @param delay
/// @return
static char pt_delay(struct pt *pt, uint32 delay)
{
    static SwTimer_s ti;

    PT_BEGIN(pt);
    hal_timer.interval(&ti, delay);
    PT_WAIT_UNTIL(pt, hal_timer.expired(&ti));
    PT_END(pt);
}

/// @brief HCI 帧解析接收。未收完前一直返回0
/// @param com
/// @param func
/// @return
static uint16_t multi_frame_recv(void *para, const uint8_t *msg, uint16_t len)
{
    ble_chn_t *chn = (ble_chn_t *)para;
    if(chn->rxbuf.len + len < chn->bufsize)    /// 如果空间足够，则存入缓冲。否则丢弃
    {
        memcpy(chn->rxbuf.str + chn->rxbuf.len, msg, len);
        chn->rxbuf.len += len;
        if(chn->rx_len == 0)    /// 接收到的第一个数据块。解析帧总长度
        {
            if(chn->rxbuf.len >= 3) chn->rx_len = chn->rxbuf.str[2] + 3;    /// 第2个字节位数据包长度
        }

        if(chn->rx_len != 0 && chn->rxbuf.len >= chn->rx_len)    /// 接收到完整帧
        {
            return chn->rxbuf.len;
        }
    }
    return 0;
}

/// @brief 命令请求
/// @param chn
/// @param request
/// @return
static char bt_command(ble_chn_t *chn, uint8_t request)
{
    static SwTimer_s tim;
    uint8_t          result = 0;
    uint16_t         len;

    PT_BEGIN(&chn->atpt);
    chn->ret   = false;
    chn->param = (void *)ble_cmd_list[request].cmd;
    len        = ble_cmd_list[request].request(com_tx_buf, chn->param);
    if(len)
    {
        DBG_PRINTF(P_BLE, D, "\r\nBT command tx:");
        DBG_PRINTF(P_BLE, M, com_tx_buf, len);
        bt_uart_send(com_tx_buf, len);
    }
    // while(hal_uart.send_over_query(COM_BLE)) {}

    hal_timer.interval(&tim, ble_cmd_list[request].time_out);
    if(request == 17 && chn->ready) hal_timer.interval(&tim, 20);
    PT_WAIT_UNTIL(&chn->atpt, chn->lock == 0);
    chn->lock++;
    PT_WAIT_UNTIL(&chn->atpt, (len = hal_uart.frame_recv(COM_BLE, multi_frame_recv, chn)) || hal_timer.expired(&tim));
    if(len != 0 || chn->rxbuf.len != 0)
    {
        DBG_PRINTF(P_BLE, D, "BT command rx:");
        DBG_PRINTF(P_BLE, M, com_rx_buf, len);

        chn->ret = true;
        result   = ble_cmd_list[request].parse(com_rx_buf, len);    // result: 0-err, 1-true, >2 -继续接收
        chn->ret = result > 0 ? true : false;
    }
    else
    {
        chn->ret = false;    // 超时
    }
    if(request == 0 && chn->poweron) chn->ret = true;
    if(request == 17 && chn->ready) chn->ret = true;
    chn->rx_len    = 0;
    chn->rxbuf.len = 0;
    chn->lock--;
    PT_END(&chn->atpt);    /// BT命令请求结束
}

/// @brief 模块应用请求
/// @param chn
/// @param trytimes
/// @param delay
/// @param request
/// @return
static char module_request(ble_chn_t *chn, uint8_t trytimes, uint16_t delay, uint8_t request)
{
    static uint8_t repeat = 0;

    PT_BEGIN(&chn->trypt);
    do {
        PT_SPAWN(&chn->trypt, &chn->atpt, bt_command(chn, request));    // 生成指令请求线程
        if(!chn->ret)
        {
            DBG_PRINTF(P_BLE, D, "\r\n>> err_Request = %d, repeat = %d <<\r\n", request, repeat);
            PT_WAIT_THREAD(&chn->trypt, pt_delay(&chn->atpt, delay));    // 超时后延时重试
        }
    } while(++repeat < trytimes && !chn->ret);
    repeat          = 0;                         /// 重置重试次数
    chn->error_code = chn->ret ? 0 : request;    /// 错误码

    PT_END(&chn->trypt);
}

/// @brief 蓝牙配置参数
/// @param state
/// @return
static char ble_configurate(ble_sta_t *state)
{
    static uint8_t i   = 0;
    ble_chn_t     *chn = state->chn;

    PT_BEGIN(&state->mpt);

    PT_WAIT_THREAD(&state->mpt, pt_delay(&chn->atpt, 1000));    // 等待电源稳定
    /// 复位模块
    bsp.module_off(INT_MOD);                                  // rst低电平有效
    PT_WAIT_THREAD(&state->mpt, pt_delay(&chn->atpt, 25));    // RST有效信号宽度20ms
    bsp.module_on(INT_MOD);                                   // 完成复位

    DBG_PRINTF(P_BLE, D, "\r\nble configurate...");
    PT_WAIT_THREAD(&state->mpt, pt_delay(&chn->atpt, 20));    // 等待复位完成

    state->chn->poweron = false;
    state->chn->ready   = false;
    state->chn->conn    = false;
    /// 指令配置
    for(i = 0; i < eleof(ble_cmd_list); i++)
    {
        PT_WAIT_THREAD(&state->mpt, module_request(chn, 1, 100, i));
        // PT_WAIT_THREAD(&state->mpt, pt_delay(&chn->atpt, 1)); // 通信设置后短暂等待
        if(chn->error_code) { break; }    /// 出错，退出配置流程
    }

    if(!chn->error_code)
    {
        // state->work_status = BLE_CONNEECT;
        state->work_status = BLE_TRANSFER_STATUS;
        chn->ret           = false;
        PT_EXIT(&state->mpt);    /// 完成初始化，退出配置线程
    }
    else
        state->work_status = BLE_CFG_STATUS;

    DBG_PRINTF(P_BLE, D, "\r\n>> err_InitAt code = %d <<", chn->error_code);
    PT_WAIT_THREAD(&state->mpt, pt_delay(&chn->atpt, 2000));    // 出错，延时2s退出配置线程
    // ble_init();
    PT_END(&state->mpt);    /// 初始化失败，结束配置线程
}

// /// @brief 查询连接状态
// /// @param state
// /// @return
// static char ble_connect_verify(ble_sta_t *state)
// {
//     static uint8_t i   = 0;
//     ble_chn_t     *chn = state->chn;

//     PT_BEGIN(&state->mpt);
//     chn->ret = false;

//     for(i = 0; i < eleof(ble_cmd_list); i++)
//     {
//         PT_WAIT_THREAD(&state->mpt, module_request(chn, 1, 100, i));
//         if(chn->error_code) { break; }    /// 出错，退出配置流程
//     }

//     if(!chn->error_code)
//     {
//         state->work_status = BLE_TRANSFER_STATUS;
//         PT_EXIT(&state->mpt);    /// 完成初始化，退出配置线程
//     }

//     PT_WAIT_THREAD(&state->mpt, pt_delay(&chn->atpt, 2000));    // 出错，延时2s退出配置线程
//     // ble_init();
//     PT_END(&state->mpt);    /// 初始化失败，结束配置线程
// }

/// @brief 蓝牙透明传输
/// @param state
/// @return
static char ble_transparent(ble_sta_t *state)
{
    static SwTimer_s blk_timer;
    ble_chn_t       *chn = state->chn;
    uint16_t         len = hal_uart.frame_recv(COM_BLE, multi_frame_recv, chn);

    PT_BEGIN(&state->mpt);

    if(chn->rxbuf.len) hal_timer.interval(&blk_timer, 3000);                                        // 收到数据开启定时器
    PT_WAIT_UNTIL(&state->mpt, (len != 0) || (chn->rxbuf.len && hal_timer.expired(&blk_timer)));    // 等待接收数据或者超时
    if(len != 0 || chn->rxbuf.len != 0)                                                             // 如果正确收到或者收到数据后超时，都认为接收完成
    {
        if(chn->rxbuf.str[0] == BT_EVENT)
        {
            // if((chn->rxbuf.str[1] == HCI_EVENT_STAUS_RESPONSE && chn->rxbuf.str[2] == 0x01 && (chn->rxbuf.str[3] & 0x20)) || (chn->rxbuf.str[1] == HCI_EVENT_BLE_DISCONNECTED))
            // {
            //     ble_state.work_status = BLE_CONNEECT;
            // }
            // else
            if(chn->rxbuf.str[1] == HCI_EVENT_BLE_DATA_RECEIVED)
            {
                if(chn->rxbuf.len > 5 && chn->rxbuf.len < chn->msg_maxsize)
                {
                    chn->ret     = true;
                    chn->msg_len = chn->rxbuf.len - 5;
                    memcpy(chn->msg, chn->rxbuf.str + 5, chn->msg_len);
                    if(chn->msg_len > 15)
                    {
                        // 解析645协议长度
                        
                    }
                }
            }
        }
        DBG_PRINTF(P_BLE, D, "BLE rx_len=%d, recieved:%d", chn->rx_len, chn->rxbuf.len);
        DBG_PRINTF(P_BLE, M, chn->rxbuf.str, chn->rxbuf.len);
        chn->rxbuf.len        = 0;
        ble_state.chn->rx_len = 0;
    }
    PT_END(&state->mpt);
}

/// @brief BLE模块控制主线程处理
/// @param ble_main_thread
PT_THREAD(ble_main_thread(struct pt *pt))
{
    PT_BEGIN(pt);

    while(1)
    {
        if(ble_state.work_status == BLE_INIT_STATUS)
        {
            PT_WAIT_THREAD(pt, pt_delay(&ble_state.chn->atpt, 100));    // 无处理，等待外部调用初始化触发状态转移
            DBG_PRINTF(P_BLE, D, "Current BLE state: BLE_CFG_STATUS\r\n");
        }
        else if(ble_state.work_status == BLE_CFG_STATUS)
        {
            PT_SPAWN(pt, &ble_state.mpt, ble_configurate(&ble_state));    //
#if P_BLE
            if(ble_state.work_status == BLE_TRANSFER_STATUS) { DBG_PRINTF(P_BLE, D, "Current BLE state: BLE_TRANSFER_STATUS\r\n"); }
#endif
        }
        // else if(ble_state.work_status == BLE_CONNEECT)
        // {
        //     PT_SPAWN(pt, &ble_state.mpt, ble_configurate(&ble_state));    //
        //     DBG_PRINTF(P_BLE, D, "Current BLE state: BLE_TRANSFER_STATUS\r\n");
        // }
        else
        {
            PT_SPAWN(pt, &ble_state.mpt, ble_transparent(&ble_state));    //
        }
    }

    PT_END(pt);
}

/* Public functions ----------------------------------------------------------*/
/// @brief BLE模块初始化
/// @param com
/// @param rxbuf
/// @param bufsize
void ble_init(uint8_t *rxbuf, uint16_t bufsize)
{
    PT_INIT(&pt_ble_process);

    memset(&ble_state, 0, sizeof(ble_sta_t));
    memset(&ble_channel, 0, sizeof(ble_chn_t));
    memset(bt_catch_buff, 0, sizeof(bt_catch_buff));
    ble_state.chn = &ble_channel;

    ble_state.chn->msg         = rxbuf;
    ble_state.chn->msg_maxsize = bufsize;

    ble_state.chn->bufsize   = sizeof(bt_catch_buff);
    ble_state.chn->rxbuf.str = bt_catch_buff;
    bt_uart_open();
    ble_pt_restart();

    ble_state.work_status = BLE_CFG_STATUS;
}

/// @brief BLE模块接收数据处理
/// @param
/// @return
uint16_t ble_recv(void)
{
    uint16_t len;

    ble_main_thread(&pt_ble_process);
    if(ble_state.work_status != BLE_TRANSFER_STATUS) return 0;    /// 如果还没进入透传mode，则直接返回0
    if(!ble_state.chn->ret) return 0;                             /// 如果没有收到数据，则直接返回0
    ble_state.chn->ret     = false;
    len                    = ble_state.chn->msg_len;
    ble_state.chn->msg_len = 0;
    return len;
}

/// @brief BLE模块发送数据处理
/// @param msg
/// @param len
void ble_send(uint8_t *msg, uint16_t len)
{
    SwTimer_s blk_timer;
    uint8_t  *ptr = com_tx_buf;

    hal_timer.interval(&blk_timer, 50);
    while(hal_uart.send_over_query(COM_BLE) && !hal_timer.expired(&blk_timer)) {}    // 等待上一次发送完成。

    memset(com_tx_buf, 0x00, sizeof(com_tx_buf));
    *ptr++ = BT_CMD;
    *ptr++ = HCI_CMD_SEND_BLE_DATA;
    *ptr++ = (len + 2);
    *ptr++ = 0x2A;
    *ptr++ = 0x00;
    memcpy(ptr, msg, len), ptr += len;
    bt_uart_send(com_tx_buf, ptr - com_tx_buf);
    DBG_PRINTF(P_BLE, D, "BLE tx_len=%d", ptr - com_tx_buf);
    DBG_PRINTF(P_BLE, M, com_tx_buf, ptr - com_tx_buf);
}

/// @brief BLE模块发送数据查询是否完成
/// @param
/// @return
bool ble_send_over_query(void)
{
    return hal_uart.send_over_query(COM_BLE);
}

// end of file
