
/**
 ******************************************************************************
* @file    eeprom.c
* <AUTHOR> @date    2024
* @brief   eeprom 驱动文件，包含了eeprom的读写函数
*          
* @note    注意：注意SDA输出高采用设置IO口为输入的方式
*                注意SDA输出低采用设置IO口为推挽输出低的方式
*
******************************************************************************
*
* @note
* Copyright (C) 2024 SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/
#include "bsp_cfg.h"
#include "eeprom.h"


#define i2c_Delay()  hal_mcu.wait_us(2) // hc32l196 24Mh  ~220KHz 已测  

#define I2C_SDA_1()         gpio_set_input(PIN_EE_SDA)  //gpio_out_H(PIN_EE_SDA)
#define I2C_SDA_0()         gpio_set_output(PIN_EE_SDA) //gpio_out_L(PIN_EE_SDA)
#define I2C_SDA_INPUT()     gpio_set_input(PIN_EE_SDA)	
#define I2C_SDA_READ()      gpio_input_get(PIN_EE_SDA)  
#define I2C_SDA_OUTPUT()    gpio_set_output(PIN_EE_SDA)	

#define I2C_SCL_1()         gpio_out_H(PIN_EE_SCL)
#define I2C_SCL_0()         gpio_out_L(PIN_EE_SCL)

/// @brief I2C start condition
static void i2c_Stop(void)
{
    I2C_SCL_0();
    i2c_Delay();i2c_Delay();
    I2C_SDA_0();
    i2c_Delay();i2c_Delay();
    I2C_SCL_1();
    i2c_Delay();i2c_Delay();
    I2C_SDA_1();
}

/// @brief I2C总线SDA数据线空闲检测, 当停止条件发出后，如果SDA依旧为低，应连续发出10个以上CLK 
static void hal_sda_chk(void)
{
    int8_t i, j;
	for(j = 0; j < 3; j++)
	{
		I2C_SDA_INPUT();
		for(i = 10; i > 0; i--)
		{
			if(I2C_SDA_READ()) break;
			I2C_SCL_0();
			i2c_Delay();
			I2C_SCL_1();
			i2c_Delay();
		}
	}
    i2c_Stop();
}

/// @brief I2C start condition
/// @param  
static void i2c_Start(void)
{
    hal_sda_chk();
    //I2C_SDA_OUTPUT();  
    I2C_SCL_0();
    i2c_Delay();
    I2C_SDA_1();
    i2c_Delay();
    I2C_SCL_1();
    i2c_Delay();i2c_Delay();
    I2C_SDA_0();
    i2c_Delay();i2c_Delay();
    I2C_SCL_0();
    i2c_Delay();
}

/// @brief I2C write one byte to slave
/// @param  _ucByte: the byte to be written
static uint8_t i2c_SendByte(uint8_t _ucByte)
{
    register uint8_t i;

    for(i = 0; i < 8; i++)
    {
        I2C_SCL_0();
        if(_ucByte & 0x80) { I2C_SDA_1(); }
        else { I2C_SDA_0(); }
        _ucByte <<= 1;
        i2c_Delay();
        I2C_SCL_1();
        i2c_Delay();
    }
    I2C_SCL_0();
    i2c_Delay();
    I2C_SDA_INPUT();
    I2C_SCL_1();
    i2c_Delay();
    _ucByte = I2C_SDA_READ();
    I2C_SCL_0();
    I2C_SDA_OUTPUT();
    return _ucByte;
}

/// @brief I2C read one byte from slave
/// @return the read byte
static uint8_t i2c_ReadByte(void)
{
    uint8_t i;
    uint8_t value;

    I2C_SDA_INPUT();
    value = 0;
    for(i = 0; i < 8; i++)
    {
        I2C_SCL_1();
        i2c_Delay();
        value <<= 1;
        if(I2C_SDA_READ()) { value++; }
        I2C_SCL_0();
        i2c_Delay();
    }
    return value;
}

/// @brief I2C write one byte to slave with NACK
static void i2c_Ack(void)
{
    I2C_SDA_OUTPUT();
    I2C_SDA_0();
    __NOP();__NOP();__NOP();__NOP();
    I2C_SCL_1();
    i2c_Delay();
    I2C_SCL_0();
    i2c_Delay();
    I2C_SDA_1();
}
/// @brief I2C write one byte to slave with NACK
static void i2c_NAck(void)
{
    //I2C_SDA_OUTPUT();
    I2C_SDA_1();
    __NOP();__NOP();__NOP();__NOP();
    I2C_SCL_1();
    i2c_Delay();
    I2C_SCL_0();
    i2c_Delay();
}

/// 以下是eeprom驱动，修改

/// @brief   向EEPROM发送地址
/// @param   addr - EEPROM的绝对地址
/// @return  TRUE - 收到应答  FALSE - 未收到
static uint8_t eep_send_addr(uint32_t addr)
{
#if (EXTEE_TYPE <=  16)
    if(i2c_SendByte(0xa0 | ((char)(addr >> 7) & 0x0E))) { return FALSE; }
    if(i2c_SendByte((char)addr))                        { return FALSE; }
#elif (EXTEE_TYPE <= 512)
    if(i2c_SendByte(0xa0))              { return FALSE; }
    if(i2c_SendByte((char)(addr >> 8))) { return FALSE; }
    if(i2c_SendByte((char)addr))        { return FALSE; }
#else
    if(i2c_SendByte(0xa0 | ((char)(addr >> 15) & 0x0E))){ return FALSE; }
    if(i2c_SendByte((char)(addr >> 8)))                 { return FALSE; }
    if(i2c_SendByte((char)addr))                        { return FALSE; }
#endif

    return TRUE;
}
/// @brief 
/// @param addr 单页内写num个字节到EEPROM中
/// @param pdat 预写数据的地址指针
/// @param num  预写数据的字节个数
/// @return     TRUE - 正确  FALSE - 错误
static uint8_t eep_write_in_page(uint32_t addr, const uint8_t* pdat, uint16_t num)
{
    uint8_t i;
    for(i = 20; i > 0; i--)
    {
        i2c_Start();
        if(!eep_send_addr(addr)) { goto eep_iic_stop2; }

        while(num--)
        {
            if(i2c_SendByte((pdat == NULL) ? 0 : *pdat++)){ goto eep_iic_stop2; }
        }
        i2c_Stop();
        return TRUE;
    eep_iic_stop2:
        i2c_Stop();
        hal_timer.xms(1);
    }

    return FALSE;
}
/// @brief 检查EEPROM中的数据是否写入正确
/// @param addr EEPROM的绝对地址
/// @param pdat 对比数据存放地址
/// @param len  对比数据字节数
/// @return     TRUE: 写入正确
///             FALSE: 写入错误
static uint8_t eep_write_check(uint32_t addr, const uint8_t* pdat, uint16_t len)
{
    uint8_t i;
    for(i = 20; i > 0 ; i--)
    {
        i2c_Start();
		if(!eep_send_addr(addr)) { goto eep_iic_stop3; }
        i2c_Start();
	    #if (EXTEE_TYPE <= 16)
		if(i2c_SendByte(0xa1 | ((char)(addr >> 7) & 0x0E)))    { goto eep_iic_stop3; }
	    #elif (EXTEE_TYPE <= 512)
		if(i2c_SendByte(0xa1)) { goto eep_iic_stop3; }
	    #else
		if(i2c_SendByte(0xa1 | ((char)(addr >> 15) & 0x0E)))   { goto eep_iic_stop3; }
	    #endif

        while(len > 0)
        {
        	uint8_t tmp = (pdat == NULL) ? 0 : *pdat++;
            i = i2c_ReadByte();
            if(i != tmp)
            {
            	i2c_NAck();
            	break;
            }
            else
            {
            	i2c_Ack();
                len--;
            }
        }
        if(!len) i2c_NAck();

        i2c_Stop();
		return boolof(len == 0);

    eep_iic_stop3:
        i2c_Stop();
        hal_timer.xms(1);
    }

	return FALSE;
}

/// @brief I2C read bytes from slave
/// @param _pReadBuf    存放读出数据的地址
/// @param _usAddress   EEPROM的绝对地址
/// @param _usSize      读出数据字节数
/// @return           TRUE - 成功  FALSE - 失败
uint8_t bsp_eeprom_read(uint32_t _usAddress, void *_pReadBuf, uint16_t _usSize)
{
    uint16_t i;
    uint8_t* p = (uint8_t*)_pReadBuf;

    hal_gpio.ext_eeprom(GPIO_OPEN);
    //Init_I2CPort();
    for(i = 20; i > 0; i--)
    {
        i2c_Start();
        if(!eep_send_addr(_usAddress)) { goto eep_iic_stop; }

        i2c_Start();
    #if (EXTEE_TYPE <= 16)
        if(i2c_SendByte(0xa1 | ((char)(_usAddress >> 7) & 0x0E))){ goto eep_iic_stop; }
    #elif (EXTEE_TYPE <= 512)
        if(i2c_SendByte(0xa1)) { goto eep_iic_stop; }
    #else
        if(i2c_SendByte(0xa1 | ((char)(_usAddress >> 15) & 0x0E))){ goto eep_iic_stop; }
    #endif
        for(i = 0; i < _usSize; i++)
        {
            p[i] = i2c_ReadByte();
            if(i != _usSize - 1) { i2c_Ack(); }
            else { i2c_NAck(); }
        }
        i2c_Stop();
        return TRUE;
    eep_iic_stop:
        i2c_Stop();
        hal_timer.xms(1);
    }

    return FALSE;
}
/// @brief I2C write bytes to slave
/// @param _pWriteBuf  待写数据存放地址
/// @param _usAddress  待写数据EEPROM的绝对地址
/// @param _usSize     待写数据字节数
/// @return           TRUE - 成功  FALSE - 失败
uint8_t bsp_eeprom_write(uint32_t addr, const void* pdat, uint16_t len)
{
    uint8_t i;
    uint16_t plen;
    const uint8_t* p = (const uint8_t*)pdat;

    hal_gpio.ext_eeprom(GPIO_OPEN);
    //Init_I2CPort();
	if(addr + len >= EE_SIZE) return FALSE; // 写入越界
    while(len > 0)
    {
        if(addr % EE_PAGE_SIZE + len > EE_PAGE_SIZE)
        {
            plen = (uint16_t)(EE_PAGE_SIZE - addr % EE_PAGE_SIZE);
            len -= plen;
        }
        else
        {
            plen = len;
            len  = 0;
        }

        for(i = 4; i > 0; i--)
        {  
            HAL_WDG_RESET();
            if(eep_write_check(addr, p, plen)) break;
            if(!eep_write_in_page(addr, p, plen)) return FALSE;

        }
        if(i == 0) return FALSE;

        addr = addr + plen;
        if(p != NULL) p += plen;
    }

    return TRUE;
}


/// @brief 声明eeprom子模块对象
const struct eeprom_s eeprom =
{
    .read       = bsp_eeprom_read,
    .write      = bsp_eeprom_write,
};
