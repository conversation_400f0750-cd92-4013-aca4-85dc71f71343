/**
 ******************************************************************************
 * @file    remote_module.h
 * <AUTHOR> @date    2024
 * @brief
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2025  SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/
#ifndef __REMOTE_MODULE_H__
#define __REMOTE_MODULE_H__

#include "typedef.h"

#define M_ML307R_DL 1    // ML307R DL模块
#define M_N706 2         // N706模块

#define REMOTE_MODULE_TYPE M_ML307R_DL

typedef enum module_task_e
{
    M_TASK_INIT = 0,     // 初始化
    M_TASK_CFG,          // 配置模块
    M_TASK_TCP,          // TCP/IP连接
    M_TASK_NORMAL,       // 正常运行,数据传输模式
    M_TASK_INFO_GET,     // 访问模块
    M_TASK_NTP_SYNC,     // NTP时间同步任务
    M_TASK_FILE_RECV,    // 文件接收任务
    M_TASK_IDLE          // 空闲状态
} MODULE_TASK_t;

typedef enum request_type_e
{
    REQUEST_NONE = 0,
    REQUEST_TCP_OPEN,     // TCP连接请求
    REQUEST_RTC_SYNC,     // RTC同步请求
    REQUEST_DATA_SYNC,    // 数据同步请求
    REQUEST_FILE_RECV     // 文件接收请求
} REQUEST_TYPE_t;

typedef struct r_module_state_struct
{
    uint16_t cache_len;          // 模块接收缓存区长度
    uint16_t ce_error_code;      // 错误码
    uint16_t sim_state;          // SIM卡状态
    uint16_t tcp0_open_parse;    // TCP0连接解析状态
    bool     no_sim;             // 无SIM卡
    bool     net_state;          // 网络状态,只表示能联网，不代表已连上主站
    bool     tcp0_state;         // TCP0连接状态
    bool     login_req;          // 告知应用层需要登录主站，应用层调用后清零
} r_module_state_s;

struct remote_module_s
{
    r_module_state_s *state;    // 模块状态参数

    /// @brief  初始化模块
    void (*init)(uint8 *rxbuf, uint16_t bufsize);

    /// @brief  接收数据
    uint16_t (*recv)(void);

    /// @brief  模块发送数据
    void (*send)(uint8_t *msg, uint16_t len);

    /// @brief 停电上报，使用polling方式发送
    void (*lastgasp_send)(uint8_t *msg, uint16_t len, uint8_t typ);

    /// @brief  模块任务请求
    bool (*access_request)(REQUEST_TYPE_t type);

    MODULE_TASK_t (*task_state)(void);

    /// @brief  查询模块是否发送完成
    bool (*send_over_query)(void);
};

extern const struct remote_module_s remote_m;

#endif /* __REMOTE_MODULE_H__ */
