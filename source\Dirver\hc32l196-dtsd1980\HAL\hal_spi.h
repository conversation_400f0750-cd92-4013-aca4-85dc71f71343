/**
  ******************************************************************************
  * @file    hal_spi.h
  * <AUTHOR> @date    2024
  * @brief   SPI总线驱动头文件.
  *
  ******************************************************************************
  *
  * @note
  * Copyright (C) 2024 SheWei Electrics Co.,Ltd. All rights reserved.
  *
  *
  ******************************************************************************/
#ifdef __cplusplus
 extern "C" {
#endif

#ifndef __HAL_SPI_H
#define __HAL_SPI_H

/* Includes ------------------------------------------------------------------*/
#include "hal_def.h"

/* Export typedef -----------------------------------------------------------*/
/* Export define ------------------------------------------------------------*/
#define HAL_SPI0      0
#define HAL_SPI_SW0   SW0
#define HAL_SPI_SW1   SW1

/* Export macro -------------------------------------------------------------*/
#define hal_spix_deviceon(x)   hal_spi##x##_deviceon()
#define hal_spix_deviceoff(x)  hal_spi##x##_deviceoff()
#define hal_spix_trans(x, da, m)  hal_spi##x##_trans(da,m)
#define hal_spix_open(x, s)    hal_spi##x##_open(s)
#define hal_spix_close(x)      hal_spi##x##_close()

#define hal_spi_deviceon(n)    hal_spix_deviceon(n)
#define hal_spi_deviceoff(n)   hal_spix_deviceoff(n)
#define hal_spi_trans(n, da, m)   hal_spix_trans(n, da, m)
#define hal_spi_open(n, s)     hal_spix_open(n, s)
#define hal_spi_close(n)       hal_spix_close(n)

/* Exported functions -------------------------------------------------------*/
///硬件SPI
extern void hal_spi0_deviceon(void);
extern void hal_spi0_deviceoff(void);
extern char hal_spi0_trans(uint8_t ch, uint8_t m);
extern void hal_spi0_open(uint16_t kbps);
extern void hal_spi0_close(void);

// 软件模拟SPI SW0
extern void hal_spiSW0_deviceon(void);
extern void hal_spiSW0_deviceoff(void);
extern char hal_spiSW0_trans(uint8_t ch, uint8_t m);
extern void hal_spiSW0_open(uint16_t kbps);
extern void hal_spiSW0_close(void);

// 软件模拟SPI SW1
extern void hal_spiSW1_deviceon(void);
extern void hal_spiSW1_deviceoff(void);
extern char hal_spiSW1_trans(uint8_t ch, uint8_t m);
extern void hal_spiSW1_open(uint16_t kbps);
extern void hal_spiSW1_close(void);
#endif /* __HAL_SPI_H */

/** @} */
/** @} */
#ifdef __cplusplus
}
#endif

