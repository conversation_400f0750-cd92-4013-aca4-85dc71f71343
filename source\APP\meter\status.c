/**
 ******************************************************************************
* @file    status.c
* <AUTHOR> @date    2024
* @brief   电表状态处理
*
******************************************************************************
*
* @note
* Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/
#include "app.h"
#include "status.h"
#include "sysinit.h"
#include "datastore.h"
#include "power_event.h"
#include "api.h"
#include "RelayApp.h"
#if SW_PAYMENT_EN
#include "payment.h"
#endif

#define STATUS_CRC16                0  ///只能在测试阶段修改，产品发布后不得再修改！！！！否则会丢失数据
#define CRC16_CHK(struct,len)       STRUCT_CRC16_CHK(STATUS_CRC16, struct, len)
#define CRC16_CAL(struct,len)       STRUCT_CRC16_GET(STATUS_CRC16, struct, len)

#define STATUS_PARA_ADDR    nvm_addr(NVM_STATUS_PARA)
#define STATUS_DATA_ADDR    nvm_addr(NVM_STATUS_DATA)
#define STATUS_PDR_ADDR     nvm_addr(NVM_STATUS_PDR)

#define THD_TIME_MAG_DST            5   /// 磁场干扰检测时间，单位 s
#define COVER_PWR_ON_FILTER_MS      300 /// 默认300ms的开盖滤波电平
#define COVER_PWR_DN_FILTER_CT      100 /// 默认100次的开盖滤波电平
#define COVER_OP_S_CNT              1   /// 1次确认开盖
#define COVER_OP_E_CNT              1   /// 1次确认开盖

typedef struct
{
    int16_t tempr;            // 电表温度，单位 - 0.1摄氏度
    int16_t ext_batt;         // 外部电池电压，单位 - 0.001V
    int16_t int_batt;         // 内部电池电压，单位 - 0.001V
}SpecialData_s;


/// 全局变量声明
extern const StatusPara_s status_default_para;

/// 全局变量定义
meter_status_s g_status;

/// 私有变量定义
SpecialData_s              specia_data;
TYPE_SPECIAL_STUS          status_event[2]; ///用于事件记录，记录事件后清理
static const StatusPara_s* status_para;     ///运行参数，指向code flash
static StatusData_t        status_data;     ///运行数据，存放在eeprom 
static special_status_s    status_ins;      ///瞬时状态
static special_status_s    status_pwrdn;    ///电源断电期间状态
static pwrdn_rcd_s         pwrdn_rcd;       ///电源断电期间事件
static uint16_t            status_rcd_cnt[STATUS_RCD_NUM]; ///电源断电期间事件计数器

static void status_para_recovery(void)
{
    status_para = &status_default_para;
}

static void status_para_save(uint8_t typ)
{
    // StatusData_t data;
    status_para = &status_default_para;
}
/// @brief 状态数据存储
/// @param[in] typ 0-只算校验，1-保存数据
/// @return none
static bool status_data_save(uint8_t typ)
{
    CRC16_CAL(&status_data, sizeof(status_data));
    return typ ? nvm.write(STATUS_DATA_ADDR, &status_data, sizeof(status_data)) : true;
}
/// @brief 状态数据恢复
/// @param[in] none
/// @return none    
static void status_data_recovery(void)
{
    if(CRC16_CHK(&status_data, sizeof(status_data)) == false)
    {
        nvm.read(STATUS_DATA_ADDR, &status_data, sizeof(status_data));
        if(CRC16_CHK(&status_data, sizeof(status_data)) == false)
        {
            memset(&status_data, 0, sizeof(status_data));
            CRC16_CAL(&status_data, sizeof(status_data));
            nvm.write(STATUS_DATA_ADDR, &status_data, sizeof(status_data));
        }
    }
}

/// @brief 数据存储
/// @param[in] typ 0-只算校验，1-保存数据
/// @return none
static bool status_pwrdn_data_save(uint8_t typ)
{
    CRC16_CAL(&pwrdn_rcd, sizeof(pwrdn_rcd));
    return typ ? nvm.write(STATUS_PDR_ADDR, &pwrdn_rcd, sizeof(pwrdn_rcd)) : true;
}

/// @brief 数据恢复
/// @param[in] none
/// @return none    
static void status_pwrdn_data_recovery(void)
{
    if(CRC16_CHK(&pwrdn_rcd, sizeof(pwrdn_rcd)) == false)
    {
        nvm.read(STATUS_PDR_ADDR, &pwrdn_rcd, sizeof(pwrdn_rcd));
        if(CRC16_CHK(&pwrdn_rcd, sizeof(pwrdn_rcd)) == false)
        {
            memset(&pwrdn_rcd, 0, sizeof(pwrdn_rcd));
            CRC16_CAL(&pwrdn_rcd, sizeof(pwrdn_rcd));
            nvm.write(STATUS_PDR_ADDR, &pwrdn_rcd, sizeof(pwrdn_rcd));
        }
    }
}

/// @brief 电表状态刷新
/// @param[in] none
/// @return none
static void meter_status_refresh(void)
{
    pe_status_s *pe_status = power_event.status_get(1);

    g_status.power.vol_loss     = pe_status->v.vol_loss;
    g_status.power.vol_ovr      = pe_status->v.vol_ovr;
    g_status.power.vol_low      = pe_status->v.vol_low;
    g_status.power.ph_miss      = pe_status->v.ph_miss;
    g_status.power.vol_unb      = pe_status->v.vol_unb;
    g_status.power.vol_rev_seq  = pe_status->v.vol_rev_seq;

    g_status.load.i_loss        = pe_status->i.i_loss;
    g_status.load.i_high        = pe_status->i.i_high;
    g_status.load.i_miss        = pe_status->i.i_miss;
    g_status.load.i_unb         = pe_status->i.i_unb;
    g_status.load.i_unb2        = pe_status->i.i_unb2;
    g_status.load.i_rev_seq     = pe_status->i.i_rev_seq;

    g_status.load.p_rev         = pe_status->p.p_rev;
    g_status.load.p_over        = pe_status->p.p_over;
    g_status.load.md_over       = pe_status->p.md_over;
    g_status.load.pf_low        = pe_status->p.pf_low;
    g_status.load.q_rev         = pe_status->p.q_rev;

    g_status.running.ext_bat_low = status_data.confirm.ext_batt_low;
    g_status.running.int_bat_low = status_data.confirm.int_batt_low;
    g_status.running.factory     = api.is_factory_mode();
    g_status.running.non_activity= false; //当前不支持非运营模式
#if RLY_OUTSIDE_ENABLE
    g_status.running.breaker   = (control.out_state_get(TYPE_RLY_2) == RLY_DISCONNECTED) ? true : false;
    g_status.running.rly_ready = (control.data_get(TYPE_RLY_2)->cur_state == READY_FOR_RECONNECTION) ? true : false;
#endif
#if RLY_INSIDE_ENABLE
    g_status.running.breaker = (control.out_state_get(TYPE_RLY_1) == RLY_DISCONNECTED) ? true : false;
    g_status.running.rly_ready = (control.data_get(TYPE_RLY_1)->cur_state == READY_FOR_RECONNECTION) ? true : false;
#endif
#if SW_PAYMENT_EN
    g_status.running.prepay      = true;
    {
        pay_account_s *p_pay     = pay.account_get();
        g_status.running.low_cr1 = p_pay->status.low_credit1;
        g_status.running.low_cr2 = p_pay->status.low_credit2;
        g_status.running.no_credit = p_pay->status.no_credit;
    }
#else
    g_status.running.prepay  = false;
    g_status.running.low_cr1 = 0;
    g_status.running.low_cr2 = 0;
    g_status.running.no_credit =0;
#endif

    g_status.running.top_cov_opn= status_data.confirm.top_cov_opn;
    g_status.running.bot_cov_opn= status_data.confirm.bot_cov_opn;
    g_status.running.mag_dst    = status_data.confirm.mag_dst;
    g_status.running.int_bat_low= status_data.confirm.int_batt_low;
    g_status.running.ext_bat_low= status_data.confirm.ext_batt_low;
    g_status.running.online     = true; //当前不支持远程离线
    g_status.running.malignant_load = false; //当前不支持恶性负载

    /* 自检状态 */
    g_status.self_chk.rtc     = boolof(mclock.datetime->stus.doubtful_value || mclock.datetime->stus.invalid_value);
    g_status.self_chk.uncalr = boolof(mic.ins->stus.uncal & 0x07);
    g_status.self_chk.measure = boolof(mic.ins->stus.error);
#if RLY_INSIDE_ENABLE
    g_status.self_chk.relay1_err = boolof(control.data_get(TYPE_RLY_1)->relay_err);
#endif
#if RLY_OUTSIDE_ENABLE
    g_status.self_chk.relay2_err = boolof(control.data_get(TYPE_RLY_2)->relay_err);
#endif
//=================================================================
    /* 控制状态 */
    g_status.ctrl.top_cov_opn = g_status.running.top_cov_opn;
    g_status.ctrl.bot_cov_opn = g_status.running.bot_cov_opn;
    g_status.ctrl.mag_dst     = g_status.running.mag_dst;
    g_status.ctrl.over_load   = boolof(g_status.load.p_over);

    g_status.ctrl.vol_over    = boolof(g_status.power.vol_ovr );
    g_status.ctrl.i_over      = boolof(g_status.load.i_high);
    g_status.ctrl.i_unb       = boolof(g_status.load.i_unb | g_status.load.i_unb2);
    g_status.ctrl.seq_err     = boolof(g_status.power.vol_rev_seq);
    g_status.ctrl.i_rev       = boolof(g_status.load.p_rev); // 潮流反向
#if FB_PAYMENT_ENABLE
    g_status.ctrl.debt        =  pay.data_get()->ctrl.relay_off;
#endif
    g_status.ctrl.mod_fault   = boolof(g_status.self_chk.module_in | g_status.self_chk.module_out);
    g_status.ctrl.low_vol     = boolof(g_status.power.vol_low );
    g_status.ctrl.pf_low      = boolof(g_status.load.pf_low);
    g_status.ctrl.rly_fault   = boolof(g_status.self_chk.relay1_err | g_status.self_chk.relay2_err);
    g_status.ctrl.rly_ready   = g_status.running.rly_ready;
    g_status.ctrl.clk_err     = g_status.self_chk.rtc;
    g_status.ctrl.mic_err     = boolof(g_status.self_chk.measure | g_status.self_chk.uncalr);

    g_status.ctrl.malignant_load = g_status.running.malignant_load;   
}

#if USE_LED_RELAY
static void relay_led_ctrl(void)
{
    if(g_status.running.rly_ready)
    {
        led.ctrl(RELAY_LED, FREQ_0_5HZ); /// 0.5Hz闪烁,1秒亮，1秒灭
    }
    else if(g_status.running.breaker)     ///优先指示拉闸
    {
        led.ctrl(RELAY_LED, FREQ_ON);
    }
    else if(g_status.ctrl.lword & status_para->led_ctrl_filter.lword)
    {
        led.ctrl(RELAY_LED, FREQ_0_5HZ); /// 0.5Hz闪烁,1秒亮，1秒灭
    }
    else
    {
        led.ctrl(RELAY_LED, FREQ_OFF);
    }
    
}
#endif

#if USE_BUZZER
static void beep_control(void)
{
#ifndef NDEBUG
    static uint16_t beep_test = 6000;
#endif
    uint8_t f_beep = 0;

#ifndef NDEBUG
    if(beep_test == 6000)
    {
        beep.start(200, 0, 0, 0, 0); ///开机响
        beep_test--; f_beep = 1;
    }
    else if(beep_test)
    {
        f_beep = 1; beep_test--;
    }
#endif

    if(g_status.ctrl.lword & status_para->beep_ctrl_filter.lword)
    {
        f_beep  = 1;
        beep.start(100, 400, 0, 0, 0); /// 2Hz持续鸣叫
    }
    
    if(!f_beep) beep.stop();    
}
#endif

#if USE_MAG_DST
/// @brief 磁场干扰检测,放置到秒任务
/// @param[in] none
/// @return TRUE-保存数据, 0x5A - 不保存数据
static bool mag_dst_detect(void)
{
    static uint8_t mag_dst_cnt = 0;

    status_ins.mag_dst = bsp.magnetic_state();
    if(status_ins.mag_dst)
    {
        if(status_data.confirm.mag_dst) { mag_dst_cnt = 0; }
        else
        {
            if(++mag_dst_cnt >= THD_TIME_MAG_DST)
            {
                status_data.confirm.mag_dst = 1;
                status_data.history.mag_dst = 1;
                status_event[0] |= STUS_MAG_DST_S;
                
                mag_dst_cnt = 0;
                return TRUE;
            }
        }
    }
    else
    {
        if(!status_data.confirm.mag_dst) { mag_dst_cnt = 0; }
        else
        {
            if(++mag_dst_cnt >= THD_TIME_MAG_DST)
            {
                status_data.confirm.mag_dst = 1;
                status_event[1] |= STUS_MAG_DST_E;
                mag_dst_cnt = 0;
                return TRUE;
            }
        }
    }
    return FALSE;
}
#endif
#if USE_FCOVER
/// @brief 开上盖检测,放置到秒任务
/// @param[in] none
/// @return TRUE-保存数据, 0x5A - 不保存数据
static bool f_cover_open_detect(void)
{
    static uint8_t f_cover_cnt = 0;

    BTN_STATE_t btn = key.action_get(TYPE_BTN_FCOVER, COVER_PWR_ON_FILTER_MS);

    if(btn == STA_BTN_YES)
    {
        status_ins.top_cov_opn = 1;
        if(status_data.confirm.top_cov_opn) { f_cover_cnt = 0; }
        else
        {
            if(++f_cover_cnt >= COVER_OP_S_CNT)
            {
                status_data.confirm.top_cov_opn = 1;
                status_data.history.top_cov_opn = 1;
                status_data.data[TOP_COV_OPN].time_s = mclock.datetime->u32datetime;
                status_event[0] |= STUS_TOP_OPN_S;
                f_cover_cnt = 0;
                return TRUE;
            }
        }
    }
    else if(btn == STA_BTN_NO)
    {
        status_ins.top_cov_opn = 0;
        if(!status_data.confirm.top_cov_opn) { f_cover_cnt = 0; }
        else
        {
            if(++f_cover_cnt >= COVER_OP_S_CNT)
            {
                status_data.confirm.top_cov_opn = 0;
                status_data.data[TOP_COV_OPN].time_e = mclock.datetime->u32datetime;
                status_event[1] |= STUS_TOP_OPN_E;
                f_cover_cnt = 0;
                return TRUE;
            }
        }
    }
    return FALSE;
}
#endif

#if USE_TCOVER
/// @brief 开端盖检测,放置到秒任务
/// @param[in] none
/// @return TRUE-保存数据, FALSE - 不保存数据
static bool t_cover_open_detect(void)
{
    static uint8_t t_cover_cnt = 0;

    BTN_STATE_t btn = key.action_get(TYPE_BTN_TCOVER, COVER_PWR_ON_FILTER_MS);

    if(btn == STA_BTN_YES)
    {
        status_ins.bot_cov_opn = 1;
        if(status_data.confirm.bot_cov_opn) { t_cover_cnt = 0; }
        else
        {
            if(++t_cover_cnt >= COVER_OP_S_CNT)
            {
                status_data.confirm.bot_cov_opn = 1;
                status_data.history.bot_cov_opn = 1;
                status_data.data[BOT_COV_OPN].time_s = mclock.datetime->u32datetime;
                status_event[0] |= STUS_BOT_OPN_S;
                t_cover_cnt = 0;
                return TRUE;
            }
        }
    }
    else if(btn == STA_BTN_NO)
    {
        status_ins.bot_cov_opn = 0;
        if(!status_data.confirm.bot_cov_opn) { t_cover_cnt = 0; }
        else
        {
            if(++t_cover_cnt >= COVER_OP_S_CNT)
            {
                status_data.confirm.bot_cov_opn = 0;
                status_data.data[BOT_COV_OPN].time_e = mclock.datetime->u32datetime;
                status_event[1] |= STUS_BOT_OPN_E;
                t_cover_cnt = 0;
                return TRUE;
            }
        }
    }
    return FALSE;
}
#endif

/// @brief 获取电池电压
/// @param[in] typ 0-抄读电池 1-时钟电池
/// @return 电池电压，单位 - 0.001V
int16_t get_batt_voltage(uint8_t typ)
{
    if(typ)
    {
        return specia_data.int_batt;
    }
    else
    {
        return specia_data.ext_batt;
    }
}

//获取温度
/// @param[in] none
/// @return 内部电池电压，单位 0.1°C
int16_t get_temperature(void)
{
    return specia_data.tempr;
}

/// @brief 电源断电期间事件处理
/// @param[in] none
/// @return none
void status_power_down_rcd_proc(void)
{
    /* 处理掉电开盖检测 */
#if USE_FCOVER
    if(pwrdn_rcd.pwdn_top_opn_stamp != 0) // 掉电有开面盖
    {
        status_data.confirm.top_cov_opn = 1;
        status_data.history.top_cov_opn = 1;
        status_pwrdn.top_cov_opn    = 1;
        status_data.data[TOP_COV_OPN].time_s = pwrdn_rcd.pwdn_top_opn_stamp;
        status_event[0] |= STUS_TOP_OPN_S;
    }
#endif
    /* 处理掉电开端盖检测 */
#if USE_TCOVER
    if(!g_status.running.non_activity)
    {
        if(pwrdn_rcd.pwdn_bot_opn_stamp != 0) // 掉电有开尾盖
        {
            status_data.confirm.bot_cov_opn = 1;
            status_data.history.bot_cov_opn = 1;
            status_pwrdn.bot_cov_opn    = 1;
            status_data.data[BOT_COV_OPN].time_s = pwrdn_rcd.pwdn_bot_opn_stamp;
            status_event[0] |= STUS_BOT_OPN_S;
        }
    }
#endif
    if(pwrdn_rcd.pwdn_top_opn_stamp != 0
    || pwrdn_rcd.pwdn_bot_opn_stamp != 0)
    {
        memset(&pwrdn_rcd, 0, sizeof(pwrdn_rcd_s));
        status_data_save(1);
        status_pwrdn_data_save(1);
    }
}

/// @brief 状态模块秒任务
/// @param[in] none
/// @return none
void status_second_run(void)
{
    static uint8_t power_on_filter = 2;
#if USE_ADC_EXBAT
    static uint16_t ext_batt_time;
#endif
#if USE_ADC_INBAT 
    static uint16_t int_batt_time;
#endif
    bool save_flg = FALSE;  ///是否需要保存数据标志

    if(power_on_filter){ power_on_filter--; return; } //等待电源稳定和计量稳定

    status_para_recovery();
    status_data_recovery();
    status_pwrdn_data_recovery();

    ///处理掉电期间事件
    status_power_down_rcd_proc();

    #if USE_FCOVER
    if(f_cover_open_detect() != FALSE) save_flg = TRUE;
    #endif
    #if USE_TCOVER
    if(t_cover_open_detect() != FALSE) save_flg = TRUE;
    #endif

    /// 外部抄读电池电压监测
#if USE_ADC_EXBAT
    {
    uint16_t batt_st_time_thd;
    uint16_t batt_ed_time_thd;
    specia_data.ext_batt  = (int16_t)adc.value_get(TYPE_ADC_EXTBAT); 
    batt_st_time_thd = g_status.running.factory ? 6 : EXTBATT_LOW_STA_TIME; // 生产模式检测时长为6秒，便于生产快速检测电池状态
    batt_ed_time_thd = g_status.running.factory ? 6 : EXTBATT_LOW_END_TIME; // 生产模式检测时长为6秒，便于生产快速检测电池状态
    if(specia_data.ext_batt < EXTBATT_LOW_THD && !status_data.confirm.ext_batt_low)
    {
        ext_batt_time++;
        if(ext_batt_time > batt_st_time_thd)
        {
            ext_batt_time = 0;
            status_data.confirm.ext_batt_low = 1;
            status_event[0] |= STUS_EXTBATT_LOW_S;
            save_flg = true;
        }
    }
    else if(specia_data.ext_batt >= EXTBATT_NORMAL_THD && status_data.confirm.ext_batt_low)
    {
        ext_batt_time++;
        if(ext_batt_time > batt_ed_time_thd)
        {
            ext_batt_time = 0;
            status_data.confirm.ext_batt_low = 0;
            status_event[1] |= STUS_EXTBATT_LOW_E;
            save_flg = true;
        }
    }
    else
    {
        ext_batt_time = 0;
    }
    }
#endif

    /// 内部电池电压监测
#if USE_ADC_INBAT
    {
    uint16_t batt_st_time_thd2;
    specia_data.int_batt  = adc.value_get(TYPE_ADC_INBAT);
    batt_st_time_thd2 = g_status.running.factory ? 6 : INTBATT_LOW_STA_TIME; // 生产模式检测时长为6秒，便于生产快速检测电池状态
    if(specia_data.int_batt < INTBATT_LOW_THD && !status_data.confirm.int_batt_low)
    {
        int_batt_time++;
        if(int_batt_time > batt_st_time_thd2)
        {
            int_batt_time = 0;
            status_data.confirm.int_batt_low = 1;
            status_event[0] |= STUS_INTBATT_LOW_S;
            save_flg = true;
        }
    }
    else if(specia_data.int_batt > INTBATT_NORMAL_THD && status_data.confirm.int_batt_low)
    {
        int_batt_time++;
        if(int_batt_time > INTBATT_LOW_END_TIME)
        {
            int_batt_time = 0;
            status_data.confirm.int_batt_low = 0;
            status_event[1] |= STUS_INTBATT_LOW_E;
            save_flg = true;
        }
    }
    else
    {
        int_batt_time = 0;
    }
    }
#endif
#if USE_ADC_TEMPR
    specia_data.tempr = adc.value_get(TYPE_ADC_TEMPR);
#endif
#if USE_MAG_DST
    if(mag_dst_detect() != FALSE) save_flg = TRUE;
#endif
    meter_status_refresh();
     // 保存数据
    if(save_flg) status_data_save(save_flg);
}

/// @brief 状态模块实时运行
/// @param[in] none
/// @return none
void status_idle_run(void)
{
#if USE_LED_RELAY
    relay_led_ctrl(); ///led控制
#endif
#if USE_BUZZER
    beep_control();  ///蜂鸣器控制
#endif
}

/// @brief 获取事件状态
/// @param state 
/// @return 
bool status_event_query(TYPE_SPECIAL_STUS state)
{
    bool ret = FALSE;
    uint16_t idx = (uint16_t)state & 0x01;
    state &= 0xFFFE;
    return boolof(status_event[idx] & state);
}
/// @brief 清除事件状态
/// @param  
void status_state_clr(void)
{
    status_event[0] = 0;
    status_event[1] = 0;
}

void status_init(void)
{
    sysinit.self_checking();
    status_para_recovery();
    status_data_recovery();

    if(!bsp.state_query(STUS_BSP_PWR_ON))
    {
    // 掉电下第一次必须扫描一下开盖状态，防止掉电一瞬间开盖的漏记录
    #if USE_FCOVER
        status_rcd_cnt[TOP_COV_OPN] = 1; bsp.wakeup_init(TOP_COV_WAKEUP);
    #endif
    #if USE_TCOVER
        status_rcd_cnt[BOT_COV_OPN] = 1; bsp.wakeup_init(BOT_COV_WAKEUP);
    #endif
    }
}

void status_task_power_off_run(void)
{
    bool save_flag = false;

    if(!bsp.wakeup_state(TOP_COV_WAKEUP|BOT_COV_WAKEUP)) return;

    status_pwrdn.top_cov_opn  = boolof(status_data.confirm.top_cov_opn || pwrdn_rcd.pwdn_top_opn_stamp );
    status_pwrdn.bot_cov_opn  = boolof(status_data.confirm.bot_cov_opn || pwrdn_rcd.pwdn_bot_opn_stamp );
    if(status_pwrdn.top_cov_opn) { bsp.wakeup_close(TOP_COV_WAKEUP); }
    if(status_pwrdn.bot_cov_opn) { bsp.wakeup_close(BOT_COV_WAKEUP); }
#if USE_FCOVER
    
    if((status_rcd_cnt[TOP_COV_OPN] || bsp.wakeup_state(TOP_COV_WAKEUP))
    && (!status_pwrdn.top_cov_opn)) // 减少重复判断及重复记录掉电时间
    {
        key.wakeup_scan(TYPE_BTN_FCOVER, COVER_PWR_DN_FILTER_CT);
        if(key.state_get(TYPE_BTN_FCOVER) == STA_BTN_SHORT)
        {
            status_rcd_cnt[TOP_COV_OPN]++;
            if(status_rcd_cnt[TOP_COV_OPN] >= 3)
            {
                save_flag = true; //保存标志
                status_pwrdn.top_cov_opn = 1;
                pwrdn_rcd.pwdn_top_opn_stamp = mclock.datetime->u32datetime;
                status_rcd_cnt[TOP_COV_OPN] = 0;
                bsp.wakeup_close(TOP_COV_WAKEUP);
            }
        }
        else status_rcd_cnt[TOP_COV_OPN] = 0;
    }
#endif

    if(!g_status.running.non_activity)
    {
    #if USE_TCOVER
        
        if((status_rcd_cnt[BOT_COV_OPN] || bsp.wakeup_state(BOT_COV_WAKEUP))
        && (!status_pwrdn.bot_cov_opn)) // 减少重复判断及重复记录掉电时间
        {
            key.wakeup_scan(TYPE_BTN_TCOVER, COVER_PWR_DN_FILTER_CT);
            if(key.state_get(TYPE_BTN_TCOVER) == STA_BTN_SHORT)
            {
                status_rcd_cnt[BOT_COV_OPN]++;
                if(status_rcd_cnt[BOT_COV_OPN] >= 3)
                {
                    save_flag = true; //保存标志
                    status_pwrdn.bot_cov_opn    = 1;
                    pwrdn_rcd.pwdn_bot_opn_stamp = mclock.datetime->u32datetime;
                    status_rcd_cnt[BOT_COV_OPN] = 0;
                    bsp.wakeup_close(BOT_COV_WAKEUP);
                }
            }
            else status_rcd_cnt[BOT_COV_OPN] = 0;
        }
    #endif
    }
    status_pwrdn_data_save(save_flag);
}

special_status_s* status_get(FD_STUS_MODE mode)
{
    if(mode == FD_STUS_INSTANT)
    {
        return &status_ins;
    }
    else if(mode == FD_STUS_CONFIRM)
    {
        return &status_data.confirm;
    }
    else if(mode == FD_STUS_HISTORY)
    {
        return &status_data.history;
    }
    else
    {
        return &status_pwrdn;
    }
}

status_rcd_data_s *status_rcd_data_get(status_rcd_t typ)
{
    return &status_data.data[typ];
}

/// @brief 状态模块复位
/// @param[in] none
/// @return none
void status_reset(uint8_t type)
{
    if (type & SYS_PARA_RESET)
    {
        status_para_save(1);
    }
    if (type & SYS_DATA_RESET)
    {
        memset(&status_data, 0, sizeof(StatusData_t));
        status_data_save(boolof(type != SYS_GLOBAL_RESET));
        status_event[0] = 0;
        status_event[1] = 0;
    }
}

uint16_t status_645status_get(uint8_t typ)
{
    uint16_t status = 0;
    pe_data_s *pe_ptr = power_event.data_get();
    switch(typ)
    {
        case 1:
            {
                status_word1_t *word = (status_word1_t *)&status;
                word->int_bat_low = status_data.confirm.int_batt_low;
                word->ext_bat_low = status_data.confirm.ext_batt_low;
                word->p_rev       = boolof(pe_ptr->confirm.p.p_rev);
                word->q_rev       = boolof(pe_ptr->confirm.p.q_rev);
            }
            break;
        case 2:
            {
                status_word2_t *word = (status_word2_t *)&status;
                word->a_p_rev     = boolof(pe_ptr->confirm.p.p_rev&0x01);
                word->b_p_rev     = boolof(pe_ptr->confirm.p.p_rev&0x02);
                word->c_p_rev     = boolof(pe_ptr->confirm.p.p_rev&0x04);
                word->a_q_rev     = boolof(pe_ptr->confirm.p.q_rev&0x01);
                word->b_q_rev     = boolof(pe_ptr->confirm.p.q_rev&0x02);
                word->c_q_rev     = boolof(pe_ptr->confirm.p.q_rev&0x04);
            }
            break;
        case 3:
            {
                status_word3_t *word = (status_word3_t *)&status;
                word->rly_status  = g_status.running.breaker;
                word->meter_type  = PAY_MODE;
            }
            break;
        case 4:
        case 5:
        case 6:
            {
                uint8_t i = (typ-4);
                status_word456_t *word = (status_word456_t *)&status;
                word->los_v       = boolof(pe_ptr->confirm.v.vol_loss&(0x01<<i));
                word->low_v       = boolof(pe_ptr->confirm.v.vol_low &(0x01<<i));
                word->over_v      = boolof(pe_ptr->confirm.v.vol_ovr &(0x01<<i));
                word->los_i       = boolof(pe_ptr->confirm.i.i_loss  &(0x01<<i));
                word->over_i      = boolof(pe_ptr->confirm.i.i_high  &(0x01<<i));
                word->over_p      = boolof(pe_ptr->confirm.p.p_rev   &(0x01<<i));
                word->rev_p       = boolof(pe_ptr->confirm.p.p_over  &(0x01<<i));
                word->mis_ph      = boolof(pe_ptr->confirm.v.ph_miss &(0x01<<i));
                word->mis_i       = boolof(pe_ptr->confirm.i.i_miss  &(0x01<<i));
            }
            break;
        case 7:
            {
                status_word7_t *word = (status_word7_t *)&status;
                word->v_rev_sqr   = boolof(pe_ptr->confirm.v.vol_rev_seq);
                word->i_rev_sqr   = boolof(pe_ptr->confirm.i.i_rev_seq);
                word->v_unbalance = boolof(pe_ptr->confirm.i.i_rev_seq);
                word->i_unbalance = boolof(pe_ptr->confirm.i.i_rev_seq);
                word->i_rev_sqr   = boolof(pe_ptr->confirm.i.i_rev_seq);
                word->i_rev_sqr   = boolof(pe_ptr->confirm.i.i_rev_seq);
            }
            break;
    }
    
    return status;
}

/// @brief 声明hal_rtc子模块对象
const struct m_status_s mstatus =
{
    .reg                    = &g_status,
    .reset                  = status_reset,
    .state_query            = status_event_query,
    .state_clr              = status_state_clr,
    .get_645status          = status_645status_get,
    .battery_voltage_get    = get_batt_voltage,
    .temperature_get        = get_temperature,
    .status_get             = status_get,
    .rcd_data_get           = status_rcd_data_get,
};

/// @brief 继电器控制模块任务接口
const struct app_task_t status_task =
{
    .init           = status_init,
    .second_run     = status_second_run,
    .idle_run       = status_idle_run,
    .power_off_init = status_init,
    .power_off_run  = status_task_power_off_run,
};

///end of file


