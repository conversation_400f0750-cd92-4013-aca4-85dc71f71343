/**
 ******************************************************************************
* @file    typedef.h
* <AUTHOR> @date    2024
* @brief   类型定义
*
******************************************************************************
*
* @note
* Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/

#ifdef __cplusplus
 extern "C" {
#endif

#ifndef __TYPEDEF_H
#define __TYPEDEF_H

#include <assert.h>
#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>
#include <stdlib.h>

/* \brief Portable data type defines */
typedef signed char        int8,  s8;
typedef unsigned char      uint8, u8;
typedef signed short       int16, s16;
typedef unsigned short     uint16,u16;
typedef signed long        int32, s32;
typedef unsigned long      uint32,u32;
typedef float              fp32,  f32;
typedef double             fp64,  f64;
typedef signed long long   int64, s64;
typedef unsigned long long uint64,u64;


/* \brief Function pointer define */
typedef void (*funcPointer) (void);

/* \brief Bits string defines */
typedef union
{
    struct { char b0:1; char b1:1; char b2:1; char b3:1;
             char b4:1; char b5:1; char b6:1; char b7:1; };
    uint8_t h;
} bits8_t;
typedef union
{
    struct { char b0 :1; char b1 :1; char b2 :1; char b3 :1;
             char b4 :1; char b5 :1; char b6 :1; char b7 :1;
             char b8 :1; char b9 :1; char b10:1; char b11:1;
             char b12:1; char b13:1; char b14:1; char b15:1; };
    uint16_t hh;
} bits16_t;
typedef union
{
    struct { char b0 :1; char b1 :1; char b2 :1; char b3 :1;
             char b4 :1; char b5 :1; char b6 :1; char b7 :1;
             char b8 :1; char b9 :1; char b10:1; char b11:1;
             char b12:1; char b13:1; char b14:1; char b15:1;
             char b16:1; char b17:1; char b18:1; char b19:1;
             char b20:1; char b21:1; char b22:1; char b23:1;
             char b24:1; char b25:1; char b26:1; char b27:1;
             char b28:1; char b29:1; char b30:1; char b31:1; };
    uint32_t hhhh;
} bits32_t;

/* \brief Combin defines, it is very useful */
typedef union { uint8_t b[2]; uint16_t w; } u8_16_t, union16;
typedef union { uint8_t b[4]; uint16_t w[2]; uint32_t l; } u8_32_t, union32;
#ifdef __LONG_LONG_SIZE__
typedef union { uint8_t b[8]; uint16_t w[4]; uint32_t l[2]; uint64_t ll; } u8_64_t, union64;
#endif

/* \brief Octet string defines */
typedef struct { size_t len; const char* str; } cstring_t;
typedef struct { size_t len; uint8_t* str; } octstr_t, string_t;
typedef struct { size_t len; const uint8_t* str; } coctstr_t;

#define MAX_SECONDS 0xBC19137F   //最大的秒数，转换为时间戳时，超过该值则表示无效时间戳，99-12-31 23:59:59

#ifndef TRUE
#define TRUE          1
#endif

#ifndef FALSE
#define FALSE         0
#endif

#ifndef NULL
#define NULL   (void*)0
#endif

#ifndef st
#define st(x)         do { x } while (__LINE__ == -1)
#endif

/* \brief Get bit mask value from the bit no. ect. the mask of third bit is 0x0008 */
#ifndef bitmask
#define bitmask(x)     (1u << (x))
#endif

#ifndef lbitmask
#define lbitmask(x)    (1ul << (x))
#endif

#ifndef vbitmask
#define vbitmask(x, v) (((uint32_t)(v)) << (x))
#endif

/**
  * \brief  Translate expression statement or variable to bool value.
  * \param  [in]  x-the statement.
  * \return The result TRUE(1) or FALSE(0) in \a x.
  */
#ifndef boolof
#define boolof(x)    ((x) ? TRUE : FALSE)
#endif

/// 返回数组元素个数
/// x-数组名称
#ifndef eleof
#define eleof(x)     (sizeof(x) / sizeof((x)[0]))
#endif


#ifdef member_offset
#undef member_offset
#endif

/// 定义结构体成员偏移, 返回偏移量
/// T-结构体名称
/// member-成员名称
/// \note 该宏定义的目的是为了兼容不同平台的结构体成员偏移定义
#ifndef member_offset
#define member_offset(T, member)   ((size_t)(&(((T*)0)->member)))
#endif

/// 在结构体数组中获取第num个元素的偏移地址
#ifndef offsetptr
#define offsetptr(T, num)     ((size_t)((T*)0 + (num)))
#endif

/// 定义结构体成员大小，返回成员大小
#ifndef member_size
#define member_size(T, member)    (sizeof(((T*)0)->member))
#endif



/// x数据实际长度，size对齐单位，返回分配对齐长度
#ifndef alignof
#define alignof(x,size)  ((size)*(((uint32)(x)+(size)-1)/(size)))
#endif

/**
  * \brief  Counts the trailing zero bits of the given value considered as a 32-bit integer.
  * \param  [in]  u-Value of which to count the trailing zero bits.
  * \return The count of trailing zero bits in \a u.
  */
#if (defined __GNUC__) || (defined __CC_ARM)
#define ctz(u)              __builtin_ctz(u)
#else
#define ctz(u)             ((u) & (1ul <<  0) ?  0 : \
                            (u) & (1ul <<  1) ?  1 : \
                            (u) & (1ul <<  2) ?  2 : \
                            (u) & (1ul <<  3) ?  3 : \
                            (u) & (1ul <<  4) ?  4 : \
                            (u) & (1ul <<  5) ?  5 : \
                            (u) & (1ul <<  6) ?  6 : \
                            (u) & (1ul <<  7) ?  7 : \
                            (u) & (1ul <<  8) ?  8 : \
                            (u) & (1ul <<  9) ?  9 : \
                            (u) & (1ul << 10) ? 10 : \
                            (u) & (1ul << 11) ? 11 : \
                            (u) & (1ul << 12) ? 12 : \
                            (u) & (1ul << 13) ? 13 : \
                            (u) & (1ul << 14) ? 14 : \
                            (u) & (1ul << 15) ? 15 : \
                            (u) & (1ul << 16) ? 16 : \
                            (u) & (1ul << 17) ? 17 : \
                            (u) & (1ul << 18) ? 18 : \
                            (u) & (1ul << 19) ? 19 : \
                            (u) & (1ul << 20) ? 20 : \
                            (u) & (1ul << 21) ? 21 : \
                            (u) & (1ul << 22) ? 22 : \
                            (u) & (1ul << 23) ? 23 : \
                            (u) & (1ul << 24) ? 24 : \
                            (u) & (1ul << 25) ? 25 : \
                            (u) & (1ul << 26) ? 26 : \
                            (u) & (1ul << 27) ? 27 : \
                            (u) & (1ul << 28) ? 28 : \
                            (u) & (1ul << 29) ? 29 : \
                            (u) & (1ul << 30) ? 30 : \
                            (u) & (1ul << 31) ? 31 : \
                            32)
#endif

/**
  * \brief Calls the routine at address \a addr. It generates a long call opcode.
  * \param [in]  addr-Address of the routine to call.
  * \note  It may be used as a long jump opcode in some special cases.
  */
#define long_call(addr)     ((*(void (*)(void))(addr))())

/// 声明存储关联定义
/// \param t 数据类型
/// \param n 成员名称
/// \param l 保留数据长度，如果预留长度太小，将导致编译错误
#if defined(WIN32)
#define SMALLOC(t,n,l) union{t n; uint8 buf_##n[l*((l/sizeof(t))/(l/sizeof(t)))]; uint8 test_##n[l*((l/sizeof(t))/(l/sizeof(t))) - 1];}
#else
#define SMALLOC(t,n,l) union{t n; uint8 buf_##n[l*((l/sizeof(t))/(l/sizeof(t)))];}
#endif

/** \brief Stringize.
 *
 * Stringize a preprocessing token, this token being allowed to be \#defined.
 *
 * May be used only within macros with the token passed as an argument if the
 * token is \#defined.
 *
 * For example, writing STRINGZ(PIN) within a macro \#defined by PIN_NAME(PIN)
 * and invoked as PIN_NAME(PIN0) with PIN0 \#defined as A0 is equivalent to
 * writing "A0".
 */
#define STRINGZ(x)                                #x

/** \brief Absolute stringize.
 *
 * Stringize a preprocessing token, this token being allowed to be \#defined.
 *
 * No restriction of use if the token is \#defined.
 *
 * For example, writing ASTRINGZ(PIN0) anywhere with PIN0 \#defined as A0 is
 * equivalent to writing "A0".
 */
#define ASTRINGZ(x)                               STRINGZ(x)

//Swap a 16-bit integer
#define _SWAP16(x) ( \
   (((x) & 0x00FF) << 8) | \
   (((x) & 0xFF00) >> 8))

//Swap a 32-bit integer
#define _SWAP32(x) ( \
   (((x) & 0x000000FFUL) << 24) | \
   (((x) & 0x0000FF00UL) << 8) | \
   (((x) & 0x00FF0000UL) >> 8) | \
   (((x) & 0xFF000000UL) >> 24))

//Swap a 64-bit integer
#define _SWAP64(x) ( \
   (((x) & 0x00000000000000FFULL) << 56) | \
   (((x) & 0x000000000000FF00ULL) << 40) | \
   (((x) & 0x0000000000FF0000ULL) << 24) | \
   (((x) & 0x00000000FF000000ULL) << 8) | \
   (((x) & 0x000000FF00000000ULL) >> 8) | \
   (((x) & 0x0000FF0000000000ULL) >> 24) | \
   (((x) & 0x00FF000000000000ULL) >> 40) | \
   (((x) & 0xFF00000000000000ULL) >> 56))

#endif /* __TYPEDEF_H */

/** @} */
/** @} */
#ifdef __cplusplus
}
#endif
