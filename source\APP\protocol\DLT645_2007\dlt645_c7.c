/******************************************************************************
*    (c) Copyright 2016, SheWei Electronics Co.,Ltd.
*    All Rights Reserved
*
*    Filename:      dlt645_c7.c
*    Describe:  DLT645-2007协议，04类数据部分     
*                负荷记录数据主站下行格式说明
*    Device:
*    Compiler:
*
*    Created on:
*    Modify record:
*
*******************************************************************************/
#include "typedef.h"
#include "DLT645_2007_id.h"

/// @brief 读取数据处理
/// @param p_info 
/// @return 
static uint16_t dlt_645_read_7(DLT645_2007_MSG_S *p_info)
{
    uint8_t *p_data = p_info->snd_dat;
    uint16_t item   = ITEM(p_info->id);

    memcpy(p_data, (uint8_t *)&p_info->id, 4), p_data += 4;
    // switch(p_info->id)
    // {
    //     case DLT645_2007_ID_LOAD_RECORD:
    //         break;
    // }

    if((p_data - p_info->snd_dat) == 4)
    {
        *p_info->snd_dat = ERR_CODE_NO_DATA, p_info->err_f = TRUE; 
        return 1;
    } // 无数据
    return (uint16_t)(p_data - p_info->snd_dat);
}


/// end of file
