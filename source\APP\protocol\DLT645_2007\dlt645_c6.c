/******************************************************************************
*    (c) Copyright 2016, SheWei Electronics Co.,Ltd.
*    All Rights Reserved
*
*    Filename:      dlt645_c6.c
*    Describe:  DLT645-2007协议，04类数据部分     
*                负荷记录数据主站下行格式说明
*    Device:
*    Compiler:
*
*    Created on:
*    Modify record:
*
*******************************************************************************/
#include "typedef.h"
#include "DLT645_2007_id.h"
#include "utils.h"

static lc_get_s lc_res;

/// @brief 读取数据处理
/// @param p_info 
/// @return 
static uint16_t dlt_645_read_6(DLT645_2007_MSG_S *p_info, uint8_t *buff)
{
    uint8_t *p_data = p_info->snd_dat;
    uint8_t *p_rev  = p_info->data;
    uint16_t item   = ITEM(p_info->id);
    NVM_LOG_t idx   = NVM_LOG_UNDEF;
    uint8_t  typ    = (uint8_t)p_info->id;

    p_rev += 4; // 跳过ID
    if(p_info->frame_no == 0)
    {
        if(typ == 0)     //最早记录块
        {
            lc_res.type    = 0;
            lc_res.get_num = bcdtob(*p_rev);
        }
        else if(typ == 1) //给定时间记录块
        {
            Calendar_s start_time;
            lc_res.get_num = bcdtob(*p_rev), p_rev++;

            start_time.time.second = 0;
            start_time.time.minute = bcdtob(*p_rev), p_rev++;
            start_time.time.hour   = bcdtob(*p_rev), p_rev++;
            start_time.date.day    = bcdtob(*p_rev), p_rev++;
            start_time.date.month  = bcdtob(*p_rev), p_rev++;
            start_time.date.year   = bcdtob(*p_rev);
            lc_res.time = mclock.calendar_to_seconds(&start_time);

            lc_res.type = 1;
        }
        else if(typ == 2) //最近一个记录块
        {
            lc_res.get_num = 1;
            lc_res.type = 2;
        }
        lc_res.frame_num = 0;
    }
    else
    {
        lc_res.frame_num = p_info->frame_no;
    }

    memcpy(p_data, (uint8_t *)&p_info->id, 4), p_data += 4;
    
    switch(item)
    {
        case ITEM(C6_EARLIEST_GET(0)):    //@(0x06000000 | LP_INDEX(N)) 第0类负荷最早记录块
        // case ITEM(C6_TIME_GET(0)    ):    //@(0x06000001 | LP_INDEX(N)) 第0类负荷给定时间记录块
        // case ITEM(C6_LAST_GET(0)    ):    //@(0x06000002 | LP_INDEX(N)) 第0类负荷最近一个记录块
        
            idx = NVM_LC1_PROFILE;
            break;
        case ITEM(C6_EARLIEST_GET(1)):    //@(0x06000000 | LP_INDEX(N)) 第1类负荷最早记录块
        // case ITEM(C6_TIME_GET(1)    ):    //@(0x06000001 | LP_INDEX(N)) 第1类负荷给定时间记录块
        // case ITEM(C6_LAST_GET(1)    ):    //@(0x06000002 | LP_INDEX(N)) 第1类负荷最近一个记录块
            idx = NVM_LC1_PROFILE;
            break;
        case ITEM(C6_EARLIEST_GET(2)):    //@(0x06000000 | LP_INDEX(N)) 第2类负荷最早记录块
        // case ITEM(C6_TIME_GET(2)    ):    //@(0x06000001 | LP_INDEX(N)) 第2类负荷给定时间记录块
        // case ITEM(C6_LAST_GET(2)    ):    //@(0x06000002 | LP_INDEX(N)) 第2类负荷最近一个记录块
            break;
        case ITEM(C6_EARLIEST_GET(3)):    //@(0x06000000 | LP_INDEX(N)) 第3类负荷最早记录块
        // case ITEM(C6_TIME_GET(3)    ):    //@(0x06000001 | LP_INDEX(N)) 第3类负荷给定时间记录块
        // case ITEM(C6_LAST_GET(3)    ):    //@(0x06000002 | LP_INDEX(N)) 第3类负荷最近一个记录块
            break;
        case ITEM(C6_EARLIEST_GET(4)):    //@(0x06000000 | LP_INDEX(N)) 第4类负荷最早记录块
        // case ITEM(C6_TIME_GET(4)    ):    //@(0x06000001 | LP_INDEX(N)) 第4类负荷给定时间记录块
        // case ITEM(C6_LAST_GET(4)    ):    //@(0x06000002 | LP_INDEX(N)) 第4类负荷最近一个记录块
            break;
        case ITEM(C6_EARLIEST_GET(5)):    //@(0x06000000 | LP_INDEX(N)) 第5类负荷最早记录块
        // case ITEM(C6_TIME_GET(5)    ):    //@(0x06000001 | LP_INDEX(N)) 第5类负荷给定时间记录块
        // case ITEM(C6_LAST_GET(5)    ):    //@(0x06000002 | LP_INDEX(N)) 第5类负荷最近一个记录块
            break;
        case ITEM(C6_EARLIEST_GET(6)):    //@(0x06000000 | LP_INDEX(N)) 第6类负荷最早记录块
        // case ITEM(C6_TIME_GET(6)    ):    //@(0x06000001 | LP_INDEX(N)) 第6类负荷给定时间记录块
        // case ITEM(C6_LAST_GET(6)    ):    //@(0x06000002 | LP_INDEX(N)) 第6类负荷最近一个记录块
            break;
    }
#if 1//LC1_ENABLE || LC2_ENABLE
    if(idx != NVM_LOG_UNDEF) 
    {
        p_info->frame_f = profile.lc_fetch(idx, &lc_res, p_data);
        p_data += lc_res.size;
    } 
#endif  
    if((p_data - p_info->snd_dat) == 4)
    {
        *p_info->snd_dat = ERR_CODE_NO_DATA, p_info->err_f = TRUE; 
        return 1;
    } // 无数据
    return (uint16_t)(p_data - p_info->snd_dat);
}


/// end of file
