/******************************************************************************
*    (c) Copyright 2016, SheWei Electronics Co.,Ltd.
*    All Rights Reserved
*
*    Filename:      local_port.c
*    Describe:      端口参数设置和数据存储，锁定和挂起功能待完善。
*
*    Device:
*    Compiler:
*
*    Created on:
*    Modify record:
*
*******************************************************************************/
#include "local_port.h"
#include "timeapp.h"
#include "comm_phy.h"
#include "datastore.h"
#include "crc.h"
#include "app_config.h"

#define LOCAL_PORT_CRC16            0  ///只能在测试阶段修改，产品发布后不得再修改！！！！否则会丢失数据
#define CRC16_CHK(struct,len)       STRUCT_CRC16_CHK(LOCAL_PORT_CRC16, struct, len)
#define CRC16_CAL(struct,len)       STRUCT_CRC16_GET(LOCAL_PORT_CRC16, struct, len)

#define LOCAL_PORT_PARA_ADDR        nvm_addr(NVM_LOCAL_PORT_PARA)
#define LOCAL_PORT_DATA_ADDR        nvm_addr(NVM_LOCAL_PORT_DATA)


typedef struct 
{
    uint16_t verify_remain_time;  ///认证剩余时间
}port_running_data_s;


extern const LocalPortPara_s local_port_default_para[];
static port_running_data_s port_running_data[PHY_CHN_NUM]; 
static LocalPortData_s local_port_data[PHY_CHN_NUM]; 

/// @brief 通讯口数据存储
/// @param chn 通道
/// @param typ 
static void local_port_data_store(uint8_t chn, uint8_t typ)
{
    LocalPortData_s *pdata = &local_port_data[chn];
    uint32_t addr = LOCAL_PORT_DATA_ADDR + sizeof(LocalPortData_s) * chn;

    CRC16_CAL(pdata, sizeof(LocalPortData_s));
    if(typ) nvm.write(addr, pdata, sizeof(LocalPortData_s));
}

/// @brief 通讯口数据恢复
/// @param chn 通道
static void local_port_data_recover(uint8_t chn)
{
    LocalPortData_s *pdata = &local_port_data[chn];
    uint32_t addr = LOCAL_PORT_DATA_ADDR + sizeof(LocalPortData_s) * chn;
    if(CRC16_CHK(pdata, sizeof(LocalPortData_s)) == FALSE)
    {
        nvm.read(addr, pdata, sizeof(LocalPortData_s));
        if(CRC16_CHK(pdata, sizeof(LocalPortData_s)) == FALSE)
        {
            memset(pdata, 0, sizeof(LocalPortData_s));
            CRC16_CAL(pdata, sizeof(LocalPortData_s));
            nvm.write(addr, pdata, sizeof(LocalPortData_s));
        }
    }
}

/// @brief 获取本地通讯参数
/// @param chn
/// @return
static const LocalPortPara_s* local_port_para_get(uint8_t chn)
{
    const LocalPortPara_s* para = (const LocalPortPara_s*)LOCAL_PORT_PARA_ADDR + chn;
    if(CRC16_CHK(para, sizeof(LocalPortData_s)) == FALSE) return &local_port_default_para[chn];
    return para;
}

/// @brief 设置本地通讯参数
/// @param chn
/// @param ofst
/// @param val
/// @return
static bool local_port_para_set(uint8_t chn, uint16_t ofst, const void* val)
{
    uint32_t addr = LOCAL_PORT_PARA_ADDR + offsetptr(LocalPortPara_s, chn);
    LocalPortPara_s para;
    
    if(chn >= PHY_CHN_NUM) return false;
    para = *(local_port_para_get(chn));
    switch(ofst)
    {
        case member_offset(LocalPortPara_s, baud_rate):
        para.baud_rate = *(uint8_t*)val;
        break;

        case member_offset(LocalPortPara_s, inactivity_timeout):
        para.inactivity_timeout = *(uint16_t*)val;
        break;

        case member_offset(LocalPortPara_s, hangup_cnt_thd):
        para.hangup_cnt_thd = *(uint16_t*)val;
        break;

        case member_offset(LocalPortPara_s, auth_timeout):
        para.auth_timeout = *(uint16_t*)val;
        break;

        default: return false;
    }

    CRC16_CAL(&para, sizeof(LocalPortPara_s));
    return nvm.write(addr, &para, sizeof(LocalPortPara_s));
}

/// @brief 获取本地通讯口速率
/// @param chn
/// @return
uint8_t local_port_comm_speed_get(uint8_t chn)
{
    return local_port_para_get(chn)->baud_rate;
}

/// @brief 设置本地通讯口速率
/// @param chn
/// @param val
/// @return
bool local_port_comm_speed_set(uint8_t chn, uint8_t val)
{
    return local_port_para_set(chn, member_offset(LocalPortPara_s, baud_rate), &val);
}

/// @brief 获取本地通讯口速率
/// @param chn
/// @return
uint8_t local_port_comm_speed_get_645(uint8_t chn)
{
    uint8_t speed = local_port_para_get(chn)->baud_rate;
    speed = bitmask(speed);
    return speed;
}

/// @brief 设置本地通讯口速率
/// @param chn
/// @param val
/// @return
// Bit7 Bit6 Bit5 Bit4 Bit3 Bit2 Bit1 Bit0
// 保留 19200bps 9600bps 4800bps 2400bps 1200bps 600bps 保留
bool local_port_comm_speed_set_645(uint8_t chn, uint8_t val)
{
    uint8_t speed;
    
    for(speed = 0; speed < 8; speed++)  
    {
        if(val & 0x01) {break;}
        val >>= 1;
    }
    if((speed == 0) || (speed > 6) || (val & 0xFE)) return false; ///只有一个bit置1时有效，1-6有效
  
    return local_port_para_set(chn, member_offset(LocalPortPara_s, baud_rate), &speed);
}


/// @brief 获取本地通讯口超时阈值
/// @param chn
/// @return
uint16_t local_port_inactivity_timeout_get(uint8_t chn)
{
    return local_port_para_get(chn)->inactivity_timeout;
}

/// @brief 设置本地通讯口超时阈值
/// @param chn
/// @param val
/// @return
bool local_port_inactivity_timeout_set(uint8_t chn, uint16_t val)
{
    return local_port_para_set(chn, member_offset(LocalPortPara_s, inactivity_timeout), &val);
}

/// @brief 获取本地通讯口认证错误挂起阈值
/// @param chn
/// @return
uint16_t local_port_hangup_thd_get(uint8_t chn)
{
    return local_port_para_get(chn)->hangup_cnt_thd;
}

/// @brief 设置本地通讯口认证错误挂起阈值
/// @param chn
/// @param val
/// @return
bool local_port_hangup_thd_set(uint8_t chn, uint16_t val)
{
    return local_port_para_set(chn, member_offset(LocalPortPara_s, hangup_cnt_thd), &val);
}

/// @brief 获取本地通讯口认证超时阈值
/// @param chn
/// @return
uint16_t local_port_auth_timeout_get(uint8_t chn)
{
    return local_port_para_get(chn)->auth_timeout;
}

/// @brief 设置本地通讯口认证超时阈值
/// @param chn
/// @param val
/// @return
bool local_port_auth_timeout_set(uint8_t chn, uint16_t val)
{
    return local_port_para_set(chn, member_offset(LocalPortPara_s, auth_timeout), &val);
}
/// @brief 认证失败调用
/// @param chn 
void local_port_auth_faile(uint8_t chn)
{
    LocalPortData_s *pdata = &local_port_data[chn];
    if(pdata->hang_up == false)
    {
        if(++pdata->hangup_cnt > local_port_hangup_thd_get(chn))
        {
            pdata->hang_up = true;
            pdata->hangup_cnt = 0;
            pdata->hang_up_date_time = mclock.datetime->u32datetime;
        }
        local_port_data_store(chn, 1);
    }
}

/// @brief 认证成功调用
/// @param chn 
void local_port_auth_success(uint8_t chn)
{
   port_running_data[chn].verify_remain_time = local_port_auth_timeout_get(chn);
}

/// @brief 认证剩余时间获取
uint16_t local_port_auth_remain_time(uint8_t chn) 
{
    return port_running_data[chn].verify_remain_time;
}

/// @brief 明文合闸密错误/未授权调用
/// @param chn 
void local_port_lock_cnt(uint8_t chn)
{
    LocalPortData_s *pdata = &local_port_data[chn];
    if(pdata->lock == false)
    {
        if(++pdata->lock_cnt >= 3)
        {
            pdata->lock = true;
            pdata->lock_cnt = 0;
            pdata->lock_date_time = mclock.datetime->u32datetime;
        }
        local_port_data_store(chn, 1);
    }
}
/// 

void local_port_reset(uint8_t type)
{
    if(type & SYS_DATA_RESET)
    {
        for(uint8_t i = 0; i < PHY_CHN_NUM; i++)
        {
            LocalPortData_s *pdata = &local_port_data[i];
            memset(pdata, 0, sizeof(LocalPortData_s));
            local_port_data_store(i, 1);
        }
    }

    if(type & SYS_PARA_RESET)
    {
        // for(uint8_t i = 0; i < PHY_CHN_NUM; i++)
        // {
        //     uint32_t addr = LOCAL_PORT_PARA_ADDR + offsetptr(LocalPortPara_s, chn);
        //     nvm.write(addr, &local_port_default_para[chn], sizeof(LocalPortPara_s));
        // }
    }
}

const struct local_port_s local_port =
{
    .init                   = local_port_data_recover,
    .reset                  = local_port_reset,
    .baud_rate_get          = local_port_comm_speed_get,
    .baud_rate_set          = local_port_comm_speed_set,
    .baud_rate_get_645      = local_port_comm_speed_get_645,
    .baud_rate_set_645      = local_port_comm_speed_set_645,
    .inactivity_timeout_get = local_port_inactivity_timeout_get,
    .inactivity_timeout_set = local_port_inactivity_timeout_set,
    .hangup_cnt_thd_get     = local_port_hangup_thd_get,
    .hangup_cnt_thd_set     = local_port_hangup_thd_set,
    .auth_timeout_get       = local_port_auth_timeout_get,
    .auth_timeout_set       = local_port_auth_timeout_set,

    .auth_success           = local_port_auth_success,
    .auth_failed            = local_port_auth_faile,
    .auth_remain_time       = local_port_auth_remain_time,
};



/// end of file
