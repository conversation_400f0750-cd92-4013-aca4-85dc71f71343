/**
 ******************************************************************************
 * @file    QGWD10376.c
 * <AUTHOR> @date    2025
 * @brief
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2025  SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/

#include "QGDW10376.h"
#include "utils.h"

static uint16_t gdw376_login_frame(uint8_t *buf)
{
    uint8_t test[] = {0x68, 0x32, 0x00, 0x32, 0x00, 0x68, 0xC9, 0x20, 0x32, 0x96,
                      0x2C, 0x00, 0x02, 0x70, 0x00, 0x00, 0x01, 0x00, 0x50, 0x16};    //"683200320068C92032962C000270000001005016";

    memcpy(buf, test, sizeof(test));    // 拷贝测试数据到缓冲区
    return sizeof(test);                // 返回数据长度
}

/// @brief 登录帧ack确认函数
/// @param buf  接收到的缓冲区
/// @param len  接收到的缓冲区长度
/// @return     返回true表示已处理，false表示未处理
static bool gdw376_login_ack(const uint8_t *buf, uint16_t len)
{
    // 这里可以添加对登录帧ack的处理逻辑
    // 如果需要处理登录ack，返回true，否则返回false
    return true;    // 返回true表示已处理
}

/// @brief 心跳包创建函数
/// @param buf 传入的缓冲区
/// @return 返回心跳包长度
static uint16_t gwd376_heartbeat_frame(uint8_t *buf)
{
    uint8_t test[] = {0x68, 0x36, 0x00, 0x36, 0x00, 0x68, 0xC9, 0x20, 0x32, 0x96, 0x2C,
                      0x00, 0x02, 0x70, 0x00, 0x00, 0x04, 0x00, 0x17, 0x6A, 0x16};    //"683600360068C92032962C00027000000400176A16";

    memcpy(buf, test, sizeof(test));    // 拷贝测试数据到缓冲区
    return sizeof(test);                // 返回数据长度
}

/// @brief 心跳帧ack确认函数
/// @param buf  接收到的缓冲区
/// @param len  接收到的缓冲区长度
/// @return     返回true表示已处理，false表示未处理
static bool gdw376_heartbeat_ack(const uint8_t *buf, uint16_t len)
{
    return true;    // 返回false表示未处理
}

uint16_t gwd_process(uint8_t chn, uint8_t *ack, uint16_t len)
{
    // 这里可以添加对消息的处理逻辑
    // 例如解析ack数据，执行相应的操作等
    // 目前未实现具体的消息处理逻辑

    return len;    // 返回处理后的数据长度
}

void gdw376_init(uint8_t chn, uint8_t *buff)
{
    // 这里可以添加对协议的初始化逻辑
    // 目前未实现具体的初始化逻辑
}

uint16_t gdw376_get_lastgasp_frame(uint8_t *buf)
{
    // 这里可以添加获取停电上报数据的逻辑
    // 目前未实现具体的停电上报数据获取逻辑
    uint8_t test[] = {0x68, 0x32, 0x00, 0x32, 0x00, 0x68, 0x4B, 0x20, 0x32, 0x96, 0x2C, 0x1A, 0x0A, 0x62, 0x00, 0x00, 0x01, 0x00, 0xE6, 0x16};

    memcpy(buf, test, sizeof(test));    // 拷贝测试数据到缓冲区
    return sizeof(test);                // 返回数据长度
}

const struct gdw376_s gdw376 = {
    .init                = gdw376_init,                  // 初始化函数未实现
    .msg_process         = gwd_process,                  // 消息处理函数未实现
    .get_heartbeat_frame = gwd376_heartbeat_frame,       // 获取心跳帧函数
    .heartbeat_ack       = gdw376_heartbeat_ack,         // 心跳帧ack确认函数未实现
    .get_login_frame     = gdw376_login_frame,           // 获取登录帧函数未实现
    .login_ack           = gdw376_login_ack,             // 登录ack确认函数未实现
    .get_lastgasp_frame  = gdw376_get_lastgasp_frame,    // 获取停电上报数据函数未实现
};
