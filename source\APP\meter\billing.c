/********************************************************************************
* @file    billing.c
* <AUTHOR> @date    2024
* @brief  
*         结算日冻结（自由月结）：在结算日时刻，存储当前的日期、时间及相关数据项，应可存储最近 12 个结算
* 日的数据；数据转存分界时刻为月末的 24 时（月初零时），或在每月的 1 日至 28 日内的整点
* 时刻；其中需量保存的是月最大需量，每月第 1 结算日转存的同时，当月的最大需量值应自动
* 复零，在其它结算日，需量数据不转存，结算日需量数据采用 DL/T 698.45 协议读出时补 NULL；
* 停电时刻错过结算时刻，上电时应能补全上 12 个结算日电能量、需量数据；
*         第一结算日：在每月的第一结算日冻结。（暂时不使用）
*         年阶梯：在每个阶梯结算日时刻，存储当前的日期、时间及相关数据项，应可存储最近
4 次的数据；
*         
*         固定阶梯结算：在第一个阶梯结算日冻结。（主要用于阶梯结算，本地费控表启用）
*
******************************************************************************
*
* @note
* Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/
#include "app.h"
#include "billing.h"
#include "timeapp.h"
#include "step_tariff.h"
#include "..\datastore\log.h"
#include "energy.h"
#include "profile.h"
#include "tariff.h"

#define BILLING_CRC16               0       ///只能在测试阶段修改，产品发布后不得再修改！！！！否则会丢失数据
#define CRC16_CHK(struct,len)       STRUCT_CRC16_CHK(BILLING_CRC16, struct, len)
#define CRC16_CAL(struct,len)       STRUCT_CRC16_GET(BILLING_CRC16, struct, len)

#define BILLING_PARA_ADDR           nvm_addr(NVM_BILLING_PARA)

typedef union 
{
    struct
    {
        uint16_t monthly     : 1; // 月结有效标志
        uint16_t mb_flag     : 1; // 结算捕获结算次数特殊处理标志
        uint16_t mpatch      : 1; // 月补结算标志
        uint16_t mb_frist    : 1; // 月第一结算日标志
        uint16_t mb_add      : 1; // 月结数据增加标志

        uint16_t step_bl     : 1; // 阶梯结算有效标志
        uint16_t step_patch  : 1; // 阶梯结算补结算标志
        uint16_t step_add    : 1; // 阶梯结算数据增加标志

        uint16_t daily       : 1; // 日结有效标志
        uint16_t daily_patch : 1; // 日结补结算标志
    };
    uint16_t val;
}bl_active_s;


extern const billing_para_s  bl_default_para;  
static const billing_para_s *bl_running_para; 

static BL_STUS     bl_out_stus;
static bl_active_s bl_active;
static clock_s     lst_bl_time[bl_num]; // 0,上一次月结时间 2,上一次日结时间 3,上一次阶梯结算时间



bool billing_last_month_billing_time_get(uint8_t point, clock_s* lst_time);
bool billing_next_month_billing_time_get(clock_s lst_time, clock_s* nxt_time);
void billing_latest_month_billing_time_get(clock_s lst_time, clock_s* latest_time);
bool billing_last_day_billing_time_get(uint8_t point, clock_s* lst_time);
bool billing_next_day_billing_time_get(clock_s lst_time, clock_s* nxt_time);
void billing_latest_day_billing_time_get(clock_s lst_time, clock_s* latest_time);
PAY_CREDIT_DATA_TYPE billing_cum_paid_get(bl_type_t bl_type, uint8_t bl_lst);

static const ENERGY_TYPE bl_energy_type_tab[BL_EN_TYPE_NUM] =
{
    TYPE_ENERGY_ADD_ACT,    // TYPE_BL_COM_ACT,     //组合有功 正+反
    TYPE_ENERGY_POS_ACT,    // TYPE_BL_EN_ADD_ACT,  //正向有功
    TYPE_ENERGY_NEG_ACT,    // TYPE_BL_EN_SUB_ACT,  //反向有功  
    TYPE_ENERGY_POS_REA,    // TYPE_BL_COM1_REA,    //组合无功1
    TYPE_ENERGY_NEG_REA,    // TYPE_BL_COM2_REA,    //组合无功2
    TYPE_ENERGY_Q1_REA,     // TYPE_BL_EN_Q1_REA,
    TYPE_ENERGY_Q2_REA,     // TYPE_BL_EN_Q2_REA,
    TYPE_ENERGY_Q3_REA,     // TYPE_BL_EN_Q3_REA,
    TYPE_ENERGY_Q4_REA      // TYPE_BL_EN_Q4_REA,
    // BL_EN_TYPE_NUM
};

static const NVM_LOG_t bl_log_nvm_tab[bl_num] = 
{
    NVM_LOG_MON_FROZEN,
    NVM_LOG_DAY_FROZEN,
    NVM_LOG_STEP_FROZEN
};

/// @brief 参数存储
/// @param ofst 
/// @param val 
/// @param len 
/// @return 
static bool bl_para_save(uint16_t ofst, const void* val, uint16_t len)
{
    billing_para_s para;
    if(ofst != 0) memcpy(&para, bl_running_para, sizeof(para));
    memcpy((uint8_t*)&para + ofst, val, len);
    CRC16_CAL(&para, sizeof(billing_para_s));
    bl_running_para = (const billing_para_s*)BILLING_PARA_ADDR;
    return nvm.write((uint32_t)bl_running_para, &para, sizeof(billing_para_s));  
}

/// @brief 参数载入
static void bl_para_load(void)
{
    bl_running_para = (const billing_para_s*)BILLING_PARA_ADDR;
    if(CRC16_CHK(bl_running_para, sizeof(billing_para_s)) == false)
    {
        bl_running_para = &bl_default_para;
    }
}

/// @brief 判断是否为结算日
/// @param typ 结算类型 0-月结 1-日结（日冻结） 2-阶梯结算
/// @return true 结算日，false 非结算日
static bool is_billing_date(clock_s clock, bl_type_t typ)
{
    clock_s bill_time;
    clock_s cur_time = clock;

    bill_time = cur_time; // 
    if(cur_time.minute != 0 || cur_time.second != 0) return false; //0分0秒
    switch(typ)
    {
        case bl_monthly: // 月结算日格式 DD-HH
            for(uint8_t i = 0; i < BILLING_MONTHLY_DAY_NUM; i++)
            {   
                bill_time.day  = bl_running_para->month_billing_time[i][0];
                bill_time.hour = bl_running_para->month_billing_time[i][1];

                if((mclock.is_valid(&bill_time) == false) || bill_time.day > BILLING_DAY_THD) //时间不对或超出范围 
                {
                    if(i == 0)  //
                    {
                        bill_time.day = 1, bill_time.hour = 0; // 第一结算日失效默认1日0时结算 
                    } 
                    else return false; // 
                }

                if(mclock.diff_value(&bill_time, &cur_time) == 0)  // 月结日匹配
                {
                    if(i == 0) bl_active.mb_frist = 1;  // 月第一结算日
                    return true;
                }
            }
            return false;


        case bl_daily: //月阶梯结算日格式 MM-DD-HH
            bill_time.hour = bl_running_para->daily_frozen_time[0];
            if(mclock.is_valid(&bill_time) == false) //时间不对 
            {
                bill_time.hour = 0, bill_time.minute = 0; // 默认0时0分结算 
            }

            if(mclock.diff_value(&bill_time, &cur_time) == 0)  // 
            {
                return true;
            }
            return false;
    #if SW_PAYMENT_EN 
        // 预付费
        case bl_step: //年阶梯结算日格式 MM-DD-HH
            {
                uint8_t step_billing_time[STEP_TARIFF_BILLING_NUM][3];
                memcpy(step_billing_time, step_tariff.active_step_get()->bill_time[0], sizeof(step_billing_time));
                for(uint8_t i = 0; i < STEP_TARIFF_BILLING_NUM; i++)
                {
                    if((typ == 1) && (i > 0)) return false; // 月阶梯结算日只支持第一阶梯结算日
                    if(typ == 2) bill_time.month = step_billing_time[i][0]; // 年阶梯结算日月份
                    bill_time.day   = step_billing_time[i][1];
                    bill_time.hour  = step_billing_time[i][2];

                    if(mclock.is_valid(&bill_time) == false) 
                    {
                        if(i == 0)  //
                        {
                            bill_time.month = 1, bill_time.day = 1, bill_time.hour = 0; // 阶梯结算日失效默认1月1日0时结算 
                        } 
                        else return false; // 
                    }

                    if(mclock.diff_value(&bill_time, &cur_time) == 0)  // 阶梯结算日匹配
                    {
                        if(i == 0) bl_active.step_bl = 1;  // 阶梯结算有效标志
                        return true;
                    }
                }
                return false;
            }
    #endif
        default:
            return false;
    }
}

/// @brief 结算数据捕获
/// @param bl_type 0 月结算 1 日结算 2 阶梯结算
/// @return 
static bool bl_blk_capture(bl_type_t bl_type)
{
    bl_monthly_s buf;
    uint8_t tf_num;
    if(hal_mcu.pwrdn_query()) return false; //掉电了，就不结算，等待上电再补
        
    /* 捕获结算时间 */
    buf.stamp = *mclock.datetime;

    /* 固定结算曲线捕获总及分费率累计电能 */
    tf_num = tariff.day_tf_num_get();
    for(uint8_t rate = 0; rate <= tf_num; rate++)
    {
        for(uint8_t type = 0; type < BL_EN_TYPE_NUM; type++)
        {
            buf.eng.cum_en[T_PHASE][type][rate] = energy.phs_cum_value_get(T_PHASE, bl_energy_type_tab[type], rate);
        }
    }
#if ENERGY_PHASE_ENABLE
    for(uint8_t rate = 0; rate <= tf_num*ENERGY_PHASE_TARIFF_EN; rate++)
    {
        for(uint8_t type = 0; type < BL_EN_TYPE_NUM; type++)
        {
            for(uint8_t ph = A_PHASE; ph <= PHASE_NUM; ph++)
            {
                buf.eng.cum_en_ph[ph - A_PHASE][type][rate] = energy.phs_cum_value_get(ph, bl_energy_type_tab[type], rate);
            }
        }
    }
#endif

#if SW_PAYMENT_EN
    /* 固定结算曲线捕获累计消费信用(用电量或者金额) */
    
#endif
    if(bl_type == bl_monthly)  ///自由月结
    {
        /* 固定月结算曲线还需捕获总及分费率最大需量及发生时间, 累计最大有功需量 */
        for(uint8_t type = 0; type < DEMAND_TYPE_NUM; type++)
        {
            for(uint8_t rate = 0; rate <= DEMAND_TARIFF_RATE_NUM; rate++)
            {
                buf.md[type][rate] = demand.max_value_get((demand_type_t)type, rate);
            }
        }

        /* 保存曲线数据 */
        if(!mlog.roll_add(log_addr(bl_log_nvm_tab[bl_type]), &buf, sizeof(bl_monthly_s))) return false;

        /* 清零最大需量数据块 */
        demand.blk_value_clr(DEMAND_TYPE_NUM);
        demand.max_value_clr();
    }
    else if(bl_type == bl_daily)  
    {
        /// 日结
        if(!mlog.roll_add(log_addr(bl_log_nvm_tab[bl_type]), &buf, sizeof(bl_step_s))) return false;
    }
    else
    {
        /// 阶梯结算
        /* 保存曲线数据 */
        if(!mlog.roll_add(log_addr(bl_log_nvm_tab[bl_type]), &buf, sizeof(bl_step_s))) return false;
    }

    return true;
}

/// @brief 结算捕获
/// @param bl_type 0 自由月结 1 月阶梯 2 年阶梯 3 日结
static void bl_capture(bl_type_t bl_type)
{
    uint16_t len;
    uint8_t  buf[MAX_CAPTURE_DATA_LEN];
    clock_s  bl_clock;

    if(hal_mcu.pwrdn_query()) return; // 掉电了，就不结算，等待上电再补

    if(bl_active.daily_patch) // 日结补结算
    {
        billing_latest_day_billing_time_get(*mclock.datetime, &bl_clock);
        bl_active.daily_patch = false;
    }
    else if(bl_active.mpatch) /// 月结补结算
    {
        billing_latest_month_billing_time_get(lst_bl_time[0], &bl_clock);
        bl_clock.stus = mclock.datetime->stus;
        bl_active.mpatch = false;
    }
    else
    {
        bl_clock = *mclock.datetime;
    }

    if(bl_type == bl_monthly) bl_active.mb_flag = true;

    /* 1.固定曲线捕获数据 */
    if(!bl_blk_capture(bl_type))
    {
        if(hal_mcu.pwrdn_query() == false) mlog.erase(log_addr(bl_log_nvm_tab[bl_type]), 1);  // 非掉电引起的捕获错误需要擦除错误的记录
        return; // 没有成功
    }

    /* 2.记录上1次结算时间 */
    lst_bl_time[bl_type] = bl_clock;
    if(bl_type == bl_monthly)
    {
        bl_active.mb_flag = false;
        bl_active.mb_add  = true;
    }
    else if(bl_type == bl_step)
    {
        bl_active.step_add = true;
    }
}


//======================= billing.c模块数据访问接口==============================
/// @brief  参数获取
/// @param  
/// @return 
const billing_para_s* billing_para_get(void)
{
    return bl_running_para;
}

/// @brief 设置参数
/// @param ofst 参数在结构体偏移地址
/// @param val  数据指针
/// @param len  数据长度
/// @return 
bool billing_para_set(uint16_t ofst, const void* val, uint16_t len)
{
    switch(ofst)
    {
        case member_offset(billing_para_s, month_billing_time): bl_out_stus |= STUS_BL_PRG_BLM_DATE; break;
    }
    return bl_para_save(ofst, val, len);
}

/// @brief 获取月结历史结算时间
/// @param point    - 上1-N次
/// @param lst_time - 用于存放结算时间的指针
/// @return true-有结算, false-无结算
bool billing_last_month_billing_time_get(uint8_t point, clock_s* lst_time)
{
    log_addr_s addr = log_addr(bl_log_nvm_tab[bl_monthly]);
    if(mlog.fetch(addr, lst_time, sizeof(clock_s), point) == 0xFFFF)  // 不存在所请求的结算
    {
        mclock.invalid_set(lst_time);
        return false;
    }
    return true;
}

/// @brief 获取以某个时间点为标准的下一个月结算时间点
/// @param lst_time - 参考时间点(上一次结算时间)
/// @param nxt_time - 用于存放下1次结算时间的指针
bool billing_next_month_billing_time_get(clock_s lst_time, clock_s* nxt_time)
{
    clock_s billing_time;

    billing_time = lst_time;
    billing_time.day  = bl_running_para->month_billing_time[0][0];
    billing_time.hour = bl_running_para->month_billing_time[0][1];
    if(!mclock.is_valid(&billing_time)) return false;

    if(lst_time.stus.invalid_value && lst_time.stus.invalid_clock_status == 0)
    {
        mclock.pdtime_get(&lst_time);
        if(lst_time.stus.invalid_value) lst_time = *mclock.datetime;
    }

    if(billing_time.day > 28) //(billing_time.day > mclock.get_month_days(billing_time.year, billing_time.month))
    {   // 第一月结日超出范围,默认到下月1日0时结算
        billing_time.month += 1;
        if(billing_time.month > 12)
        {
            billing_time.year += 1;
            billing_time.month = 1;
            if(billing_time.year > 99) return false;
        }
        billing_time.day = 1;
        billing_time.hour = 0, billing_time.minute = 0, billing_time.second = 0;
    }
    if(mclock.diff_value(&billing_time, &lst_time) <= 0)
    {
        uint8_t flag = false;

        for(uint8_t i = 1; i < BILLING_MONTHLY_DAY_NUM; i++)
        {
            if((bl_running_para->month_billing_time[i][0] > 28) ||
               (bl_running_para->month_billing_time[i][1] > 23)) break;
            billing_time.day  = bl_running_para->month_billing_time[i][0];
            billing_time.hour = bl_running_para->month_billing_time[i][1];
            // if(billing_time.day > 28 || billing_time.hour > 23) break;
            if(mclock.diff_value(&billing_time, &lst_time) > 0)
            {
                flag = true;
                break;
            }
        }
        if(flag == false)
        {
            billing_time.month += 1;
            if(billing_time.month > 12)
            {
                billing_time.year += 1;
                billing_time.month = 1;
                if(billing_time.year > 99) return false;
            }
            // return false;
        }
    }
#if 0
    if(mclock.diff_value(&billing_time.cale, &(lst_time.cale)) <= 0) // 通配解析后月结时间已过，往前调整一个月时间
    {
        billing_time.month += 1;
        if(billing_time.month > 12)
        {
            billing_time.year += 1;
            billing_time.month = 1;
            if(billing_time.year > 99) return false;
        }
        // if(bl_running_para->billing_time.day == DAY_LAST_1st)
        // {
        //     billing_time.day = mclock.get_month_days(billing_time.year, billing_time.month);
        // }
        // else if(bl_running_para->billing_time.day == DAY_LAST_2nd)
        // {
        //     billing_time.day = mclock.get_month_days(billing_time.year, billing_time.month) - 1;
        // }
        // else if(billing_time.day > mclock.get_month_days(billing_time.year, billing_time.month))
        // {
        //     billing_time.month += 1;
        //     if(billing_time.month > 12)
        //     {
        //         billing_time.year += 1;
        //         billing_time.month = 1;
        //         if(billing_time.year > 99) return false;
        //     }
        //     billing_time.day = 1;
        //     billing_time.hour = 0, billing_time.minute = 0, billing_time.second = 0;
        // }
    }
#endif

    memcpy(nxt_time, &billing_time, sizeof(clock_s));
    nxt_time->week = mclock.get_week_value(nxt_time->year, nxt_time->month, nxt_time->day);

    return true;
}

/// @brief 获取以当前时间为基准的上一次月结时间
/// @param lst_time     - 上1次结算时间
/// @param latest_time  - 
void billing_latest_month_billing_time_get(clock_s lst_time, clock_s* latest_time)
{
    clock_s nxt_time;

    while(1)
    {
        if(!billing_next_month_billing_time_get(lst_time, &nxt_time))
        {
            break;
        }
        if(mclock.diff_value(&nxt_time, mclock.datetime) > 0)
        {
            *latest_time = lst_time;
            break;
        }
        lst_time = nxt_time;
    }
}

/// @brief 获取日结历史结算时间
/// @param point    - 上1-N次
/// @param lst_time - 用于存放结算时间的指针
/// @return  true-有结算, false-无结算
bool billing_last_day_billing_time_get(uint8_t point, clock_s* lst_time)
{
    log_addr_s addr = log_addr(bl_log_nvm_tab[bl_daily]);
    if(mlog.fetch(addr, lst_time, sizeof(clock_s), point) == 0xFFFF) // 不存在所请求的结算
    {
        mclock.invalid_set(lst_time);
        return false;
    }
    return true;
}

/// @brief 获取以某个时间点为标准的下一个日结算时间点
/// @param lst_time  - 参考时间点(上一次结算时间)
/// @param nxt_time  - 用于存放下1次结算时间的指针
/// @return 
bool billing_next_day_billing_time_get(clock_s lst_time, clock_s* nxt_time)
{
    clock_s billing_time_daily;

    nxt_time->stus.value = 0;
    nxt_time->hour   = bl_running_para->daily_frozen_time[0];
    nxt_time->minute = bl_running_para->daily_frozen_time[1];
    nxt_time->second = 0;

    if((lst_time.day + 1) <= mclock.get_month_days(lst_time.year, lst_time.month))
    {
        nxt_time->year  = lst_time.year;
        nxt_time->month = lst_time.month;
        nxt_time->day   = lst_time.day + 1;
    }
    else if((lst_time.month + 1) <= 12)
    {
        nxt_time->year = lst_time.year;
        nxt_time->month = lst_time.month + 1;
        nxt_time->day = 1;
    }
    else
    {
        nxt_time->year = lst_time.year + 1;
        nxt_time->month = 1;
        nxt_time->day = 1;
    }

    nxt_time->week = mclock.get_week_value(nxt_time->year, nxt_time->month, nxt_time->day);
    return true;
}

/// @brief 获取离当前时钟最近的一次的日结算结算日时间
/// @param lst_time    
/// @param latest_time 
void billing_latest_day_billing_time_get(clock_s lst_time, clock_s* latest_time)
{
    lst_time.hour = 0, lst_time.minute = 0, lst_time.second = 0;
    *latest_time  = lst_time;
}

/// @brief 获取结算累计总电能数据
/// @param bl_type 结算类型
/// @param ph      相位T/A/B/C
/// @param bl_lst  读上N次结算的累计总电能, 0表示读当前累计总电能
/// @param bl_en   预获取的电能类型
/// @param rate    预获取电能的费率段号
/// @return        获取到的电能值
ENERGY_DEF_FORMAT billing_phs_cum_energy_get(bl_type_t bl_type, uint8_t ph, uint8_t bl_lst, BL_EN_TYPE_t bl_en, uint8_t rate)
{
    if(bl_lst == 0)
    {
        return energy.phs_cum_value_get(ph, bl_energy_type_tab[bl_en], rate); // 返回当前累计总电能
    }
    else
    {
        ENERGY_DEF_FORMAT val = 0;
        log_addr_s addr = log_addr(bl_log_nvm_tab[bl_type]);
        if(ph == T_PHASE)
            addr.ofst += (member_offset(bl_monthly_s, eng.cum_en[ph][bl_en][rate]));
    #if ENERGY_PHASE_ENABLE
        else
            addr.ofst += (member_offset(bl_monthly_s, eng.cum_en_ph[ph - A_PHASE][bl_en][rate]));
    #endif
        mlog.fetch(addr, &val, sizeof(ENERGY_DEF_FORMAT), bl_lst); // 读取上N次结算的累计总电能
        return val;
    }
}

BL_EN_TYPE_t billing_energy_type_get(uint8_t typ)
{
    for(uint8_t i = 0; i < BL_EN_TYPE_NUM; i++){ if(bl_energy_type_tab[i] == typ) return (BL_EN_TYPE_t)i;}
    return BL_EN_TYPE_NUM;
}

/// @brief 获取月结算增量电能数据
/// @param bl_type - 结算类型
/// @param ph      - 相位T/A/B/C
/// @param bl_lst  - 读上N月的增量电能, 0表示读当月增量电能
/// @param bl_en   - 预获取的电能类型
/// @param rate    - 预获取电能的费率段号
/// @return          获取到的电能增量
ENERGY_DEF_FORMAT billing_phs_inc_energy_get(bl_type_t bl_type, uint8_t ph, uint8_t bl_lst, BL_EN_TYPE_t bl_en, uint8_t rate)
{
    ENERGY_DEF_FORMAT val = 0;
    log_addr_s addr = log_addr(bl_log_nvm_tab[bl_type]);
    if(bl_lst == 0)
    {
        if(ph == T_PHASE)
            addr.ofst += (member_offset(bl_monthly_s, eng.cum_en[ph][bl_en][rate]));
    #if ENERGY_PHASE_ENABLE
        else
            addr.ofst += (member_offset(bl_monthly_s, eng.cum_en_ph[ph - A_PHASE][bl_en][rate]));
    #endif
        mlog.fetch(addr, &val, sizeof(ENERGY_DEF_FORMAT), 1); // 读出上次结算的累计电能
        return energy.phs_cum_value_get(ph, bl_energy_type_tab[bl_en], rate) - val; // 返回当前月增量电能
    }
    else
    {
        ENERGY_DEF_FORMAT last_val = 0;
        if(ph == T_PHASE)
            addr.ofst += (member_offset(bl_monthly_s, eng.cum_en[ph][bl_en][rate]));
    #if ENERGY_PHASE_ENABLE
        else
            addr.ofst += (member_offset(bl_monthly_s, eng.cum_en_ph[ph - A_PHASE][bl_en][rate]));
    #endif
        mlog.fetch(addr, &val,      sizeof(ENERGY_DEF_FORMAT), bl_lst);     // 读取上N次结算的累计电能
        mlog.fetch(addr, &last_val, sizeof(ENERGY_DEF_FORMAT), bl_lst + 1); // 读取上N+1次结算的累计电能
        return (val - last_val);
    }
}


#if SW_PAYMENT_EN
/// @brief 获取累计信用(电能，金额)
/// @param bl_type 结算类型
/// @param bl_lst  - 读上N次结算的累计总消费信用, 0表示读当前累计总消费信用
/// @return        获取到的累计总消费信用
PAY_CREDIT_DATA_TYPE billing_cum_paid_get(bl_type_t bl_type, uint8_t bl_lst)
{
    if(bl_lst == 0)
    {
        return 0; // 返回当前累计总消费信用
    }
    else
    {
        PAY_CREDIT_DATA_TYPE val = 0;
        log_addr_s addr = log_addr(bl_log_nvm_tab[bl_type]);
        addr.ofst += member_offset(bl_monthly_s, eng.cum_paid);
        mlog.fetch(addr, &val, sizeof(PAY_CREDIT_DATA_TYPE), bl_lst); // 读取上N次结算的累计总消费信用
        return val;
    }
}

/// @brief 获取上N次消费信用数据 增量
/// @param bl_lst - 读上N次消费信用, 0表示读当前消费信用
/// @param bl_type - 结算类型
/// @return        获取到的消费信用
PAY_CREDIT_DATA_TYPE billing_inc_paid_get(bl_type_t bl_type, uint8_t bl_lst)
{
    PAY_CREDIT_DATA_TYPE val = 0;
    PAY_CREDIT_DATA_TYPE tmp;
    log_addr_s addr = log_addr(bl_log_nvm_tab[bl_type]);

    if(bl_lst == 0)
    {
        addr.ofst += member_offset(bl_monthly_s, eng.cum_paid);
        mlog.fetch(addr, &val, 4, 1); // 读出上次结算的累计消费信用
        tmp = 0;//pay.data_get()->total_amount_paid.data;
        return tmp - val;      // 返回当前消费信用
    }
    else
    {
        PAY_CREDIT_DATA_TYPE last_val = 0;
        addr.ofst += member_offset(bl_monthly_s, eng.cum_paid);
        mlog.fetch(addr, &val, sizeof(PAY_CREDIT_DATA_TYPE), bl_lst); // 读取上N次结算的累计消费信用
        mlog.fetch(addr, &last_val, sizeof(PAY_CREDIT_DATA_TYPE), bl_lst + 1); // 读取上N+1次结算的累计消费信用
        //if (val < last_val) return 0;
        return (val - last_val);
    }
}

/// @brief 获取上N次日 平均消费信用
/// @param last_day - 读上N天的平均消费信用, 0表示读当日平均消费信用
/// @return         - 获取到的平均消费信用
PAY_CREDIT_DATA_TYPE billing_last_day_avg_paid(uint16_t last_day)
{
    return billing_inc_paid_get(bl_daily, last_day) / 24;
}

/// @brief 获取剩余信用使用天数
///   根据上7天平均消费信用和当前剩余电能计算剩余信用使用天数
/// @param 
/// @return  
// uint16_t billing_credit_residual_days_get(void)
// {
//     uint32_t day = 0;
//     PAY_CREDIT_DATA_TYPE val = 0;
//     uint16_t num = mlog.entry_inuse_get(log_addr(bl_log_nvm_tab[bl_daily]));
//     // PAY_CREDIT_DATA_TYPE balance = 
//     if(num >= 8)
//     {
//         val = (billing_cum_paid_get(bl_daily, 1) - billing_cum_paid_get(bl_daily, 8)) / 7;
//     }
//     if(val <= 0)
//     {
//         val = pay.default_daily_paid_get();
//     }
//     if(balance > 0)
//     {
//         day = (balance / val);
//         if(day > 9999) day = 9999;
//     }
//     return (uint16_t)day;
// }
#endif


/// @brief 获取上N次结算最大需量数据
/// @param bl_lst      - 读上N次结算的最大需量, 0表示读当前最大需量
/// @param demand_type - 需量类型
/// @param rate        - 预获取最大需量的费率段号
/// @return          获取到的最大需量
MD_reg_s billing_max_demand_get(uint8_t bl_lst, demand_type_t demand_type, uint8_t rate)
{
    if(bl_lst == 0)
    {
        return demand.max_value_get(demand_type, rate); // 返回当前最大需量
    }
    else
    {
        MD_reg_s   md;
        log_addr_s addr = log_addr(bl_log_nvm_tab[bl_monthly]);
        addr.ofst += (member_offset(bl_monthly_s, md[demand_type][rate]));
        if(mlog.fetch(addr, &md, sizeof(MD_reg_s), bl_lst) == 0xFFFF) // 读取上N月的最大需量
        {
            md.value = 0;
            // mclock.invalid_set(&md.capture_time);
        }
        if(md.value == 0) memset(&md.capture_time, 0x00, sizeof(clock_s));
        return md;
    }
}

#if DEMAND_CUM_SUM_ENABLE
/// @brief 获取月累计最大需量
/// @param bl_lst      - 读上N次结算的最大需量, 0表示当月
/// @param demand_type - 需量类型
/// @param rate        - 预获取最大需量的费率段号
/// @return            - 获取到的累计最大需量
MD_reg_s billing_cum_max_demand_get(uint8_t bl_lst, DEMAND_TYPE demand_type, uint8_t rate)
{
    uint32_t cum_md_val = 0;
    MD_reg_s cum_md;
    log_addr_s addr = log_addr(bl_log_nvm_tab[bl_monthly]);
    addr.ofst += (member_offset(bl_monthly_s, cum_md[demand_type][rate]));
    if(bl_lst == 0)
    {
        if(mlog.fetch(addr, &cum_md_val, sizeof(cum_md_val), 1) == 0xFFFF) // 读取上1月的累计最大需量
        {
            cum_md_val = 0;
        }
        MD_reg_s md = demand.max_value_get(demand_type, rate);
        cum_md.value = cum_md_val + md.value;
        cum_md.capture_time = md.capture_time;  // 当前累计最大需量的捕获时间，与当前最大需量捕获时间一致
    }
    else
    {
        if(mlog.fetch(addr, &cum_md_val, sizeof(cum_md_val), bl_lst) == 0xFFFF) // 读取上N月的最大需量
        {
            cum_md.value = 0;
            mclock.invalid_set(&cum_md.capture_time);
        }
        else
        {
            MD_reg_s lst_md;
            addr.ofst = addr.ofst - (member_offset(bl_monthly_s, cum_md[demand_type][rate]))+(member_offset(bl_monthly_s, md[demand_type][rate]));
            mlog.fetch(addr, &lst_md, sizeof(lst_md), bl_lst);
            cum_md.value = cum_md_val;
            cum_md.capture_time = lst_md.capture_time; // 上N次的累计最大需量的捕获时间，与上N次的最大需量捕获时间一致
        }
    }

    return cum_md;
}
#endif

/// @brief 远程或本地命令激活月结
void billing_command_month_billing(void)
{
    bl_active.monthly = true;
    bl_out_stus |= STUS_BL_COMMAND;
}

/* 结算次数读取 */

/// @brief 获取月结结算次数
/// @param  
/// @return 
uint32_t billing_month_billing_cnt_get(void)
{
    volatile uint32_t cnt = mlog.entries_cnt_get(log_addr(bl_log_nvm_tab[bl_monthly]));
    return bl_active.mb_flag ? (cnt + 1) : cnt;
}

/// @brief 查询是否有产生新的月结
/// @param  
/// @return 
uint8_t billing_is_happen(void)
{
    return bl_active.step_add;//bl_active.mb_add;
}


//////

/// @brief 模块初始化
/// @param  
void billing_init(void)
{
    /* 检验结算参数 */
    bl_para_load();

    /* 获取上次结算时的时钟 */
    if(!billing_last_month_billing_time_get(1, &lst_bl_time[bl_monthly]))
    {
        mclock.pdtime_get(&lst_bl_time[bl_monthly]);
    }
    if(mclock.compare(&lst_bl_time[bl_monthly]) < 0) // 时间往回调更新为当前时钟
    {
        lst_bl_time[bl_monthly] = *mclock.datetime;
    }
    if(!billing_last_day_billing_time_get(1, &lst_bl_time[bl_daily]))
    {
        mclock.pdtime_get(&lst_bl_time[bl_daily]);
    }
}

/// @brief 模块运行
/// @param  
void billing_idle_run(void)
{

}

/// @brief 秒任务
/// @param  
void billing_second_run(void)
{
    /* 检验结算参数 */
    bl_para_load();

    bl_active.mb_add   = false;  // 如果有新产生结算，这里清除标志，使用此标志的模块需放在本模块任务之后
    bl_active.step_add = false;

#if 1 // 结算日大于28号，解决时钟回调后下一个结算时间无法结算问题
    if(mclock.state_query(STUS_CLOCK_SHIFT_EVENT | STUS_CLOCK_BC_EVENT))
    {
        if(mclock.compare(&lst_bl_time[bl_monthly]) < 0) // 时间往回调更新为当前时钟
        {
            lst_bl_time[bl_monthly] = *mclock.datetime;
        }
    }
#endif

    /* 自动月结算 */
    if(is_billing_date(*mclock.datetime, bl_monthly))
    {
        bl_active.monthly = true;
        bl_out_stus |= STUS_BL_AUTO_BILLING;
    }

    if(bl_active.monthly == false)
    {
        clock_s nxt_time;
        if(billing_next_month_billing_time_get(lst_bl_time[bl_monthly], &nxt_time))
        {
            if(mclock.compare(&nxt_time) >= 0)
            {
                bl_active.monthly = true;
                bl_active.mpatch  = true;
                bl_out_stus |= STUS_BL_PATCH;
            }
        }
    }
    /// 日结
    if(is_billing_date(*mclock.datetime, bl_daily))
    {
        bl_active.daily = true;
    }
    else
    {
        clock_s nxt_time;
        if(billing_next_day_billing_time_get(lst_bl_time[bl_daily], &nxt_time))
        {
            if(mclock.compare(&nxt_time) >= 0)
            {
                bl_active.daily  = true;
                bl_active.daily_patch = true;
            }
        }
    }
    //阶梯结算
    if(is_billing_date(*mclock.datetime, bl_step))
    {
        bl_active.step_bl = true;
    }
    else
    {
        clock_s nxt_time;
        if(billing_next_day_billing_time_get(lst_bl_time[bl_step], &nxt_time))
        {
            if(mclock.compare(&nxt_time) >= 0)
            {
                bl_active.step_bl  = true;
                bl_active.step_patch = true;
            }
        }
    }

    if(bl_active.val)
    {
    #if SW_PAYMENT_EN
        if(bl_active.step_bl)  { bl_capture(bl_step);    bl_active.step_bl = false;}
    #endif
        if(bl_active.daily)    { bl_capture(bl_daily);   bl_active.daily   = false;}
        if(bl_active.monthly)  { bl_capture(bl_monthly); bl_active.monthly = false;}
    }

}

/// @brief 获取结算各状态
/// @param state 
/// @return 
bool billing_state_query(BL_STUS state)
{
    return boolof(bl_out_stus & state);
}

/// @brief 清除结算各状态
void billing_state_clr(void)
{
    bl_out_stus = 0;
}

/// @brief 清除历史月结算数据
void billing_month_profile_clr(void)
{
    mlog.empty(log_addr(NVM_LOG_MON_FROZEN));
    bl_out_stus |= STUS_BL_CLR_MDATA;
}

/// @brief 清除历史日结算数据
void billing_day_profile_clr(void)
{
    mlog.empty(log_addr(NVM_LOG_DAY_FROZEN));
    bl_out_stus |= STUS_BL_CLR_DDATA;
}
/// @brief 清除历史阶梯结算数据
void billing_step_profile_clr(void)
{
    mlog.empty(log_addr(NVM_LOG_STEP_FROZEN));
    bl_out_stus |= STUS_BL_CLR_SDATA;
}

/// @brief 复位结算模块
void billing_reset(uint8_t type)
{
    if(type & SYS_PARA_RESET)
    {
        /* 恢复默认参数 */
        bl_para_save(0, &bl_default_para, sizeof(bl_default_para));
    }

    if(type & SYS_DATA_RESET)
    {
        /* 清零数据 */
        bl_active.val = 0;
        bl_out_stus = 0;
        if(type != SYS_GLOBAL_RESET)
        {
            mlog.empty(log_addr(NVM_LOG_MON_FROZEN));
            mlog.empty(log_addr(NVM_LOG_DAY_FROZEN));
            mlog.empty(log_addr(NVM_LOG_STEP_FROZEN));
        }
        memset(lst_bl_time, 0xFF, sizeof(clock_s)* bl_num);
    }
}

const struct billing_s billing = 
{
    .reset                       = billing_reset,
    .state_query                 = billing_state_query,
    .state_clr                   = billing_state_clr,
    .month_profile_clr           = billing_month_profile_clr,
    .day_profile_clr             = billing_day_profile_clr,
    .step_profile_clr            = billing_step_profile_clr,
    .month_billing_cnt_get       = billing_month_billing_cnt_get,
    .is_happen                   = billing_is_happen,
    .command_month_billing       = billing_command_month_billing,
    .max_demand_get              = billing_max_demand_get,
#if DEMAND_CUM_SUM_ENABLE
    .cum_max_demand_get          = billing_cum_max_demand_get,
#endif
    .last_day_avg_paid           = billing_last_day_avg_paid,
    .last_day_billing_time_get   = billing_last_day_billing_time_get,
    .last_month_billing_time_get = billing_last_month_billing_time_get,
    .next_day_billing_time_get   = billing_next_day_billing_time_get,
    .next_month_billing_time_get = billing_next_month_billing_time_get,
    .para_get                    = billing_para_get,
    .para_set                    = billing_para_set,
    .energy_type_get             = billing_energy_type_get,
    .phs_cum_energy_get          = billing_phs_cum_energy_get,
    .phs_inc_energy_get          = billing_phs_inc_energy_get,

#if SW_PAYMENT_EN
    .cum_paid_get                = billing_cum_paid_get,
    .inc_paid_get                = billing_inc_paid_get,
    .last_day_avg_paid           = billing_last_day_avg_paid,
    // .credit_residual_days_get    = billing_credit_residual_days_get,
#endif
};

/// @brief billing 任务接口
const struct app_task_t billing_task =
{
    .init       = billing_init,
    .idle_run   = billing_idle_run,
    .second_run = billing_second_run,
};

// end of file
