/******************************************************************************
*    (c) Copyright 2016, SheWei Electronics Co.,Ltd.
*    All Rights Reserved
*
*    Filename:      dlt645_c1.c
*    Describe:  DLT645-2007协议，00类数据部分     
*
*    Device:
*    Compiler:
*
*    Created on:
*    Modify record:
*
*******************************************************************************/
#include "mic.h"
#include "DLT645_2007_id.h"


extern uint8_t o07_id_demand_type_get(uint32_t id, uint8_t *ph);

/// @brief 读取数据处理
/// @param p_info 
/// @return 
static uint16_t dlt_645_read_1(DLT645_2007_MSG_S *p_info, uint8_t *buff)
{
    uint8_t *p_data = buff;
    uint8_t index   = (uint8_t)(p_info->id);   // 0 当前，1-12 历史
    uint8_t rate    = (uint8_t)(p_info->id >> 8);
    uint8_t type;
    uint8_t ph;  // 分相
    uint8_t flg = 0;

    if(buff == NULL) { p_data = p_info->snd_dat; flg = 1;} //buff为空，则为通讯获取
    type = o07_id_demand_type_get(p_info->id, &ph);
    // 07协议只支持上12结算日的历史数据
    if((type == 0xFF)    || 
       (index  > BILLING_MONTH_LOG_NUM) || 
       ((rate > DEMAND_TARIFF_RATE_NUM) && (rate != 0xFF)) || 
       ((rate == 0xFF) && (index == 0xFF))) 
    { 
         if(flg){ *p_data = ERR_CODE_NO_DATA, p_info->err_f = TRUE; return 1; }
         else { memset(p_data, 0, 4); return 4; } //捕获没有的数据项返回0
    }

    if(flg) memcpy(p_data, (uint8_t *)&p_info->id, 4), p_data += 4;
    if(rate ==0xFF)
    {
        // 总，分费率数据块
        for(uint8_t i = 0; i <= DEMAND_TARIFF_RATE_NUM; i++)
        {
            MD_reg_s md = demand.max_value_get((demand_type_t)type, i);
            int32_to_lsbbcd(p_data, (uint32_t)md.value, 3), p_data += 3;
            mclock.format_to645(p_data, &md.capture_time, CLOCK_YMDhm), p_data += 5;
        }
    }
    else if(index == 0xFF)
    {
        // 当前
        MD_reg_s md = demand.max_value_get((demand_type_t)type, rate);
        int32_to_lsbbcd(p_data, (uint32_t)md.value, 3), p_data += 3;
        mclock.format_to645(p_data, &md.capture_time, CLOCK_YMDhm), p_data += 5;

        for(uint8_t i = 1; i <= BILLING_MONTH_LOG_NUM; i++)  ///< 12个历史数据
        {
            md = billing.max_demand_get(i, (demand_type_t)type, rate);
            int32_to_lsbbcd(p_data, (uint32_t)md.value, 3), p_data += 3;
            mclock.format_to645(p_data, &md.capture_time, CLOCK_YMDhm), p_data += 5;
        }
    }
    else
    {
        // 上index结算日的历史数据
        MD_reg_s md = billing.max_demand_get(index, (demand_type_t)type, rate);
        int32_to_lsbbcd(p_data, (uint32_t)md.value, 3), p_data += 3;
        mclock.format_to645(p_data, &md.capture_time, CLOCK_YMDhm), p_data += 5;
    }

    if(flg)
    {
        if((p_data - p_info->snd_dat) == 4) 
        {
            *p_info->snd_dat = ERR_CODE_NO_DATA, p_info->err_f = TRUE; 
            return 1;
        } // 无数据
        return (uint16_t)(p_data - p_info->snd_dat);
    }
    return (uint16_t)(p_data - buff);
}


static class_unit_t c1_unit_get(uint32_t id)
{
    uint8_t ph;  // 分相
    uint8_t type;
    type = o07_id_demand_type_get(id, &ph);
    switch(type)
    {
        case TYPE_DEMAND_POS_ACT:    // 正向有功
        case TYPE_DEMAND_NEG_ACT:    // 反向有功
            return UNIT_kW;
    #if DEMAND_Qx_REA_ENABLE
        case TYPE_DEMAND_Q1_REA:     // 一象限无功
        case TYPE_DEMAND_Q2_REA:     // 二象限无功
        case TYPE_DEMAND_Q3_REA:     // 三象限无功
        case TYPE_DEMAND_Q4_REA:     // 四象限无功
            return UNIT_kvar;
    #endif
    #if DEMAND_POS_APP_ENABLE
        case TYPE_DEMAND_POS_APP:    // 正向视在
        case TYPE_DEMAND_NEG_APP:    // 反向视在
            return UNIT_kVA;
    #endif
    #if DEMAND_POS_REA_ENABLE
        case TYPE_DEMAND_POS_REA:    // 正向无功
        case TYPE_DEMAND_NEG_REA:    // 反向无功
            return UNIT_kvar;
    #endif
        default:
            return UNIT_UNDEF;
    }
}
/// @brief 需量数据格式获取，用于通讯和曲线
/// @param id 
/// @return 
data_format_s dlt_645_data1_format_get(uint32_t id)
{
    data_format_s format;
    format.len = 4;
    format.type = DT_OCTET_STRING;  
    format.unit = c1_unit_get(id);
    return format;
}

/// @brief 用于曲线捕获获取数据
/// @param id 
/// @param p_data 
/// @return 
static uint16_t c1_data_get(uint32_t id, void *p_data)
{
    DLT645_2007_MSG_S p_info;
    p_info.id = id;
    return dlt_645_read_1(&p_info, (uint8_t *)p_data);
}

const dlt645_data_s c1_data =
{
    .format = dlt_645_data1_format_get,
    .data   = c1_data_get,
};


/// end of file
