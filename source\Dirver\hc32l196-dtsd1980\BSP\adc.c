/**
  ******************************************************************************
  * @file    adc.c
  * <AUTHOR> @date    2024
  * @brief   adc driver.  已测试
  *
  ******************************************************************************
  *
  * @note
  * Copyright (C) 2024 SheWei Electrics Co.,Ltd. All rights reserved.
  *
  *
  ******************************************************************************/
#include "bsp_cfg.h"
#include "adc.h"

/// 外部电池分压电阻，单位kΩ
#define EXTBAT_RESISTOR_UP  10000.0   // 外部6V电池上电阻，单位kΩ
#define EXTBAT_RESISTOR_DN  1000.0    // 外部6V电池下电阻，单位kΩ
#define EXTBAT_RATIO        ((EXTBAT_RESISTOR_UP + EXTBAT_RESISTOR_DN)/EXTBAT_RESISTOR_DN)     // 外部6V电池电压变比

/// 内部电池分压电阻，单位kΩ
#define INBAT_RESISTOR_UP   10000.0   // 内部3.6V电池上电阻，单位kΩ
#define INBAT_RESISTOR_DN   2000.0    // 内部3.6V电池下电阻，单位kΩ
#define INBAT_RATIO         ((INBAT_RESISTOR_UP + INBAT_RESISTOR_DN)/INBAT_RESISTOR_DN)        // 内部3.6V电池电压变比

/// @brief Get the external battery voltage.
/// @param  
/// @return 外部电池电压，单位0.001V
static int32_t bsp_extbat_voltage(void)
{
#ifdef ADC_CHN_EXBAT
    float vbat = hal_adc.voltage(ADC_CHN_EXBAT) * EXTBAT_RATIO - 0.02;  //手动补偿-0.02V
    return (int32_t)(vbat * 1000);
#else
    return 0.0;
#endif
}

/// @brief Get the internal battery voltage.
/// @param  
/// @return 内部电池电压，单位0.001V
static int32_t bsp_intbat_voltage(void)
{
#ifdef ADC_CHN_INBAT
    float vbat = hal_adc.voltage(ADC_CHN_INBAT) * INBAT_RATIO - 0.03;  //手动补偿-0.03V
    return (int32_t)(vbat * 1000);
#else
    return 0.0;
#endif
}

/// @brief Get the temperature of the internal battery.
/// @param  
/// @return 芯片温度,0.1摄氏度
static int32_t bsp_chip_temperature(void)
{
    float temp = hal_adc.temperature();
    return (int32_t)(temp * 10);
}

int32_t bsp_adc_get(ADC_TYPE_t ch)
{
    switch(ch)
    {
        case TYPE_ADC_TEMPR:  return bsp_chip_temperature();
        case TYPE_ADC_INBAT:  return bsp_intbat_voltage();
        case TYPE_ADC_EXTBAT: return bsp_extbat_voltage();
        default:              return 0.0;
    }
}

/// @brief ADC driver structure.   
const struct adc_s adc =
{
    .value_get = bsp_adc_get,
};





