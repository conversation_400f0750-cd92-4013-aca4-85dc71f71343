/**
  ******************************************************************************
  * @file    bsp.h
  * <AUTHOR> @version V1.0.0
  * @date    2024
  * @brief   BSP层配置文件
  *
  ******************************************************************************
  *
  * @note
  * Copyright (C) 2024 SheWei Electrics Co.,Ltd. All rights reserved.
  *
  ******************************************************************************/
#ifndef __BSP_H
#define __BSP_H

/* Includes ------------------------------------------------------------------*/
#include "bsp_cfg.h"
#include "debug.h"

/* Exported types ------------------------------------------------------------*/
/* Exported defines ----------------------------------------------------------*/
/* Exported functions ------------------------------------------------------- */
typedef uint16_t BSP_STUS;
#define STUS_BSP_PWR_ON         0x0001  // 电表正常上电标志位
#define STUS_BSP_CLK_HSE        0x0002  // 主频时钟源采用外部高速晶振
#define STUS_BSP_RTC_LSE        0x0004  // RTC时钟源采用外部32768晶振
#define STUS_BSP_RTC_RST        0x0008  // RTC域丢失(辅助电源不足)
#define STUS_BSP_PPM_ERR        0x0010  // PPM值未校准
#define STUS_BSP_POR_BOR        0x0020  // 掉电原因:BOR\POR 冷启动
#define STUS_BSP_WDG_RST        0x8000  // 看门狗异常复位

typedef uint16_t WAKEUP_STUS;
#define TOP_COV_WAKEUP          (1u << 0)
#define BOT_COV_WAKEUP          (1u << 1)
#define KEY_DISP_DN_WAKEUP      (1u << 2)
#define KEY_DISP_UP_WAKEUP      (1u << 3)
#define DISP_WAKEUP             (1u << 4)
#define SECOND_WAKEUP           (1u << 5)

typedef uint8_t MODULE_TYPE;
#define EXT_MOD                 0
#define INT_MOD                 1
#define BLE_MOD                 2
struct bsp_t
{
    void (*init)(void);              // 板级初始化函数接口
    void (*restart)(bool is_wait);   // 电路板重新复位启动函数接口
    bool (*monitor)(uint8_t mode);     // 板级电源监控任务函数接口
    bool (*state_query)(BSP_STUS mask);
    bool (*wakeup_state)(WAKEUP_STUS mask);
    void (*wakeup_init)(WAKEUP_STUS mask);
    void (*wakeup_close)(WAKEUP_STUS mask);
#if USE_MAG_DST
    bool (*magnetic_state)(void);
#endif
    void (*module_on)(MODULE_TYPE module_type);
    void (*module_off)(MODULE_TYPE module_type);
    void (*remote_module)(uint8_t typ, uint8_t mode);
    void (*pow3v3_on)(void);
    void (*pow3v3_off)(void);
    void (*ir_on)(void);
    void (*ir_off)(void);
};
extern const struct bsp_t bsp; ///板层对象


#endif /* __BSP_H */

