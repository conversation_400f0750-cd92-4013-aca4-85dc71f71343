
/**
 ******************************************************************************
* @file    status.h
* <AUTHOR> @date    2024
* @brief   电表状态处理
*
******************************************************************************
*
* @note
* Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/
#ifndef __STATUS_H
#define __STATUS_H
#include "typedef.h"



typedef uint16_t   TYPE_SPECIAL_STUS;
#define STUS_EXTBATT_LOW_S      (0 + (1U<<1))   // 外部电池低发生
#define STUS_INTBATT_LOW_S      (0 + (1U<<2))   // 内部电池低发生
#define STUS_MIC_FAULT          (0 + (1U<<3))   // 计量错误发生
#define STUS_TOP_OPN_S          (0 + (1U<<4))   // 开上盖、表盖发生
#define STUS_BOT_OPN_S          (0 + (1U<<5))   // 开端子盖发生
#define STUS_MAG_DST_S          (0 + (1U<<6))   // 磁场干扰发生


#define STUS_EXTBATT_LOW_E      (1 + (1U<<1))   // 外部电池低结束
#define STUS_INTBATT_LOW_E      (1 + (1U<<2))   // 内部电池低结束
// #define STUS_MIC_FAULT          (1 + (1U<<3))   // 计量错误发生
#define STUS_TOP_OPN_E          (1 + (1U<<4))   // 开上盖、表盖结束
#define STUS_BOT_OPN_E          (1 + (1U<<5))   // 开端子盖结束
#define STUS_MAG_DST_E          (1 + (1U<<6))   // 磁场干扰结束


typedef uint8_t                  FD_STUS_MODE;
#define FD_STUS_INSTANT          0  // 瞬时状态
#define FD_STUS_CONFIRM          1  // 确认状态
#define FD_STUS_HISTORY          2  // 历史状态
#define FD_STUS_PWDN             3  // 掉电状态下产生的状态

/// @brief 电表自检状态
typedef union
{
    uint32_t lword;
    uint16_t word[2];
    uint8_t  byte[4];
    struct
    {
        uint32_t eeprom         : 1;    ///< EEPROM 错误
        uint32_t falsh          : 1;    ///< FLASH 错误
        uint32_t crc            : 1;    ///< CRC 错误
        uint32_t wdt_rest       : 1;    ///< WDT 复位

        uint32_t rtc_osc_err    : 1;    ///< RTC晶振错误
        uint32_t relay1_err     : 1;    ///< 继电器1错误
        uint32_t relay2_err     : 1;    ///< 继电器2错误
        uint32_t ppm_err        : 1;    ///< 时钟未校准

        uint32_t rtc            : 1;    ///< RTC 错误
        uint32_t module_in      : 1;    ///< 内置模块错误
        uint32_t module_out     : 1;    ///< 外置模块错误
        uint32_t measure        : 1;    ///< 计量错误

        uint32_t uncalr         : 1;    ///< 未校表
        uint32_t por_bor        : 1;    ///< POR 或者 BOR,冷启动
    };
}self_test_s;
/// @brief 电表运行状态
typedef union
{
    uint32_t lword;
    uint16_t word[2];
    uint8_t  byte[4];
    struct
    {
        uint32_t factory        : 1;    ///< 1-工厂模式，0-用户模式
        uint32_t non_activity   : 1;    ///< 1-非运营模式，0-运营模式
        uint32_t breaker        : 1;    ///< 1-继电器拉闸
        uint32_t rly_ready      : 1;    ///< 1-继电器预合闸

        uint32_t prepay         : 1;    ///< 1-本地费控，0-远程费控
        uint32_t low_cr1        : 1;    ///< 1-余额报警1
        uint32_t low_cr2        : 1;    ///< 1-余额报警2
        uint32_t no_credit      : 1;    ///< 1-余额为0、欠费
        
        uint32_t top_cov_opn    : 1;    ///< 1-开上盖
        uint32_t bot_cov_opn    : 1;    ///< 1-开端子盖
        uint32_t mag_dst        : 1;    ///< 1-磁场干扰
        uint32_t int_bat_low    : 1;    ///< 1-内置电池电量低

        uint32_t ext_bat_low    : 1;    ///< 1-外接电池电量低
        uint32_t online         : 1;    ///< 1-远程在线
        uint32_t malignant_load : 1;    ///< 1-恶性负载
    };
}work_status_s;
/// @brief 电源状态
typedef union
{
    uint32_t lword;
    uint16_t word[2];
    uint8_t  byte[4];
    struct
    {
        uint32_t vol_loss       :3;     ///< 失压 ABC
        uint32_t vol_ovr        :3;     ///< 过压 ABC 
        uint32_t vol_low        :3;     ///< 欠压 ABC
        uint32_t ph_miss        :3;     ///< 断相 ABC
        uint32_t vol_unb        :1;     ///< 电压不平衡
        uint32_t vol_rev_seq    :1;     ///< 电压逆相序
    };
}power_status_s;
/// @brief 负载状态
typedef union
{
    uint32_t lword;
    uint16_t word[2];
    uint8_t  byte[4];
    struct
    {
        uint32_t i_loss         :3;     ///< 失流 ABC
        uint32_t i_high         :3;     ///< 过流 ABC
        uint32_t i_miss         :3;     ///< 断流 ABC
        uint32_t i_unb          :1;     ///< 电流不平衡
        uint32_t i_unb2         :1;     ///< 电流严重不平衡
        uint32_t i_rev_seq      :1;     ///< 电流逆序

        uint32_t ni_abnormal    :1;     ///< 零线电流异常
        uint32_t p_rev          :3;     // 潮流反向，bit0~2 - A~C相
        uint32_t p_over         :4;     // 过载，    bit0~3 - 总，A~C相
        uint32_t md_over        :6;     // 超需量，  bit0~3 - 总，正向有功需量，反向有功需量 Q1-Q4 需量
        uint32_t pf_low         :1;     // 总功率因素低
        uint32_t q_rev          :3;     // 无功潮流反向，bit0~3 - A-C
        //rev 4 bit
    };
}load_status_s;

/// @brief 继电器状态
typedef union
{
    uint32_t lword;
    uint16_t word[2];
    uint8_t  byte[4];
    struct
    {
        uint32_t remote         :1;    ///< 1-远程控制拉闸
        uint32_t remote_ready   :1;    ///< 1-远程控制预合闸
        uint32_t manual         :1;    ///< 1-手动控制拉闸
        uint32_t local          :1;    ///< 1-本地控制拉闸
        uint32_t over_load      :1;    ///< 1-过载拉闸
        //rev 4 bit
    };
}relay_status_s;

///LED，LCD，蜂鸣器，继电器控制  
typedef union
{
    struct
    {
        uint32_t top_cov_opn  :1;  // 开面盖
        uint32_t bot_cov_opn  :1;  // 开端盖
        uint32_t mag_dst      :1;  // 磁场干扰
        uint32_t over_load    :1;  // 超功率
        uint32_t vol_over     :1;  // 过压
        uint32_t i_over       :1;  // 过流
        uint32_t i_unb        :1;  // 电流不平衡
        uint32_t i_rev        :1;  // 电流反向

        uint32_t seq_err      :1;  // 逆相序
        uint32_t debt         :1;  // 欠费
        uint32_t mod_fault    :1;  // 模块故障或模块被拔出
        uint32_t low_vol      :1;  // 低压事件状态
        uint32_t pf_low       :1;  // 低功率因数
        uint32_t malignant_load:1; // 恶性负载
        uint32_t rly_fault    :1;  // 继电器故障
        uint32_t rly_ready    :1;  // 继电器预合闸

        uint32_t clk_err      :1;  // 时钟错误
        uint32_t mic_err      :1;  // 计量故障
        uint32_t rev          :6;  // 预留

};
    uint32_t lword;
}control_status_s;

typedef union
{
    uint16_t val;
    struct
    {
        uint16_t ext_rly_ctrl   :1; // 外部继电器控制方式 0-点平 1-脉冲
        uint16_t demand_caculate:1; // 需量计算方式 0-滑差 1-区间
        uint16_t int_bat_low    :1; // 内部电池低电压报警
        uint16_t ext_bat_low    :1; // 外部电池低电压报警
        uint16_t p_rev          :1; // 有功功率反向报警
        uint16_t q_rev          :1; // 无功功率反向报警
    };
}status_word1_t;

typedef union
{
    uint16_t val;
    struct
    {
        uint16_t a_p_rev         :1; // A相有功功率反向
        uint16_t b_p_rev         :1; // B相有功功率反向
        uint16_t c_p_rev         :1; // C相有功功率反向
        uint16_t                 :1; // 
        uint16_t a_q_rev         :1; // A相无功功率反向
        uint16_t b_q_rev         :1; // B相无功功率反向
        uint16_t c_q_rev         :1; // C相无功功率反向
    };
}status_word2_t;

typedef union
{
    uint16_t val;
    struct
    {
        uint16_t sch_no          :1; // 当前运行时段号，0-第一套，1-第二套
        uint16_t power_mode      :2; // 电源模式 0-主电源，1-辅助电源，2-电池供电
        uint16_t pragma_en       :1; // 编程允许
        uint16_t rly_status      :1; // 继电器状态
        uint16_t zone_no         :1; // 当前运行时区号，0-第一套，1-第二套
        uint16_t rly_cmd         :1; // 继电器控制命令状态，0-通，1-断
        uint16_t pre_discon_rly  :1; // 预断开继电器状态
        uint16_t meter_type      :2; // 0-非预付费，1-电能预付费，2-电费预付费
        uint16_t tf_no           :1; // 当前运费费率电价 0-第一套，1-第二套
        uint16_t step_no         :1; // 当前运行阶梯，0-第一套，1-第二套
    };
}status_word3_t;
typedef union
{
    uint16_t val;
    struct
    {
        uint16_t los_v           :1; // 失压
        uint16_t low_v           :1; // 欠压
        uint16_t over_v          :1; // 过压
        uint16_t los_i           :1; // 失流
        uint16_t over_i          :1; // 过流
        uint16_t over_p          :1; // 过功率
        uint16_t rev_p           :1; // 潮流反向
        uint16_t mis_ph          :1; // 断相
        uint16_t mis_i           :1; // 断流
    };
}status_word456_t;
typedef union
{
    uint16_t val;
    struct
    {
        uint16_t v_rev_sqr       :1; // 电压逆相序
        uint16_t i_rev_sqr       :1; // 电流逆序
        uint16_t v_unbalance     :1; // 电压不平衡
        uint16_t i_unbalance     :1; // 电流不平衡
        uint16_t aux_power_loss  :1; // 辅助电源失电
        uint16_t power_down      :1; // 电源断电
        uint16_t over_dm         :1; // 需量超限
        uint16_t low_pf          :1; // 低功率因数
        uint16_t i_unblance2     :1; // 电流严重不平衡
    };
}status_word7_t;

typedef struct
{
    self_test_s      self_chk;     ///< 自检状态
    work_status_s    running;      ///< 运行状态
    power_status_s   power;        ///< 电源状态
    load_status_s    load;         ///< 负载状态
    relay_status_s   relay;        ///< 继电器状态
    control_status_s ctrl;         ///< 控制状态led,beep
}meter_status_s;

typedef enum
{
    TOP_COV_OPN,
    BOT_COV_OPN,
    STATUS_RCD_NUM
}status_rcd_t;
typedef struct
{
    uint16_t crc;
    uint16_t chk;
    uint32_t pwdn_top_opn_stamp;          // 掉电开面盖时标
    uint32_t pwdn_bot_opn_stamp;          // 掉电开端盖时标
    uint32_t vol_all_loss_stamp;          // 全失压时标
    uint32_t vol_all_loss_i;              // 全失压电流
}pwrdn_rcd_s;

typedef struct
{
    uint16_t chk;                           // 校验
    uint16_t cs;                            // 校验
    control_status_s led_ctrl_filter;       // 跳闸灯控制掩码
    control_status_s beep_ctrl_filter;      // 蜂鸣器控制掩码
}StatusPara_s;

/// @brief 需要保存的状态数据
typedef union
{
    struct
    {
        uint16_t ext_batt_low     :1; // 外部电池低电压
        uint16_t int_batt_low     :1; // 内部电池低电压
        uint16_t measure_fault    :1; // 计量错误
        uint16_t top_cov_opn      :1; // 上盖开
        uint16_t bot_cov_opn      :1; // 端盖开
        uint16_t mag_dst          :1; // 磁场干扰
    };
    uint16_t word;
} special_status_s;

typedef struct
{
    uint32_t time_s;
    uint32_t time_e;
}status_rcd_data_s;


typedef struct
{
    uint16_t chk;     //ram校验码           
    uint16_t crc;     //crc16校验码
    special_status_s  confirm;
    special_status_s  history;    // 历史数据
    status_rcd_data_s data[STATUS_RCD_NUM];
}StatusData_t;

struct m_status_s
{
    // 状态数据
    meter_status_s* reg;

    /// @brief 状态数据初始化
    void (*reset)(uint8_t type);
    /// @brief 获取事件状态
    bool (*state_query)(TYPE_SPECIAL_STUS status);
    void (*state_clr)(void);
    /// @brief 获取电池电压
    int16_t (*battery_voltage_get)(uint8_t mode);
    /// @brief 获取温度
    int16_t (*temperature_get)(void);

    uint16_t (*get_645status)(uint8_t typ);

    special_status_s*  (*status_get)(FD_STUS_MODE mode);
    status_rcd_data_s* (*rcd_data_get)(status_rcd_t typ);
    /// @brief 模式切换
    // bool (*switch_factory_mode)(uint8_t mode);
    // bool (*switch_activity_mode)(uint8_t mode);
};
extern const struct m_status_s mstatus;
extern const struct app_task_t status_task;
#endif
